@echo off
title CyberTrap RAT - Main Launcher
color 0F

:MAIN_MENU
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🔥 CyberTrap RAT v1.0                   ║
echo ║                Professional Remote Administration Tool        ║
echo ║                                                              ║
echo ║                ⚠️  FOR EDUCATIONAL PURPOSES ONLY             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │                        MAIN MENU                             │
echo ├──────────────────────────────────────────────────────────────┤
echo │                                                              │
echo │  [1] 🔧 Setup & Install Dependencies                         │
echo │  [2] 🧪 Run Test Suite                                       │
echo │  [3] 🚀 Start RAT Server                                     │
echo │  [4] 🖥️  Start RAT Client                                     │
echo │  [5] 🔨 Build Client Executable                              │
echo │  [6] 📖 View Documentation                                   │
echo │  [7] ⚙️  Configuration                                        │
echo │  [8] 📊 Project Information                                  │
echo │  [9] ❌ Exit                                                 │
echo │                                                              │
echo └──────────────────────────────────────────────────────────────┘
echo.
set /p choice="Select option [1-9]: "

if "%choice%"=="1" goto SETUP
if "%choice%"=="2" goto TESTS
if "%choice%"=="3" goto SERVER
if "%choice%"=="4" goto CLIENT
if "%choice%"=="5" goto BUILDER
if "%choice%"=="6" goto DOCS
if "%choice%"=="7" goto CONFIG
if "%choice%"=="8" goto INFO
if "%choice%"=="9" goto EXIT

echo Invalid choice! Please select 1-9.
timeout /t 2 >nul
goto MAIN_MENU

:SETUP
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         🔧 SETUP & INSTALLATION      ║
echo ╚══════════════════════════════════════╝
echo.
echo Running setup script...
call setup.bat
echo.
echo Setup completed! Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:TESTS
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║           🧪 TEST SUITE              ║
echo ╚══════════════════════════════════════╝
echo.
echo Running test suite...
call run_tests.bat
echo.
echo Tests completed! Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:SERVER
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║          🚀 RAT SERVER               ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting CyberTrap RAT Server...
echo.
echo ⚠️ WARNING: Only use on authorized systems!
echo.
timeout /t 3
call start_server.bat
echo.
echo Server stopped! Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:CLIENT
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║          🖥️ RAT CLIENT               ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting CyberTrap RAT Client...
echo.
echo ⚠️ WARNING: Only connect to authorized servers!
echo.
timeout /t 3
call start_client.bat
echo.
echo Client stopped! Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:BUILDER
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         🔨 CLIENT BUILDER            ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting Client Builder...
echo.
echo ⚠️ WARNING: Built clients are for educational use only!
echo.
timeout /t 3
call build_client.bat
echo.
echo Build completed! Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:DOCS
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         📖 DOCUMENTATION             ║
echo ╚══════════════════════════════════════╝
echo.
echo Available Documentation:
echo.
echo [1] README.md - Main documentation
echo [2] PROJECT_INFO.md - Detailed project info
echo [3] config.json - Configuration file
echo [4] requirements.txt - Dependencies
echo.
set /p doc_choice="Select document to view [1-4] or [B]ack: "

if "%doc_choice%"=="1" (
    if exist README.md (
        type README.md | more
    ) else (
        echo README.md not found!
    )
)
if "%doc_choice%"=="2" (
    if exist PROJECT_INFO.md (
        type PROJECT_INFO.md | more
    ) else (
        echo PROJECT_INFO.md not found!
    )
)
if "%doc_choice%"=="3" (
    if exist config.json (
        type config.json | more
    ) else (
        echo config.json not found!
    )
)
if "%doc_choice%"=="4" (
    if exist requirements.txt (
        type requirements.txt | more
    ) else (
        echo requirements.txt not found!
    )
)
if /i "%doc_choice%"=="B" goto MAIN_MENU

echo.
echo Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:CONFIG
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         ⚙️ CONFIGURATION             ║
echo ╚══════════════════════════════════════╝
echo.
echo Current Configuration:
echo.
if exist config.json (
    type config.json
) else (
    echo config.json not found!
    echo Run setup first to create configuration.
)
echo.
echo [E]dit config.json or [B]ack to main menu?
set /p config_choice="Choice [E/B]: "

if /i "%config_choice%"=="E" (
    if exist config.json (
        notepad config.json
    ) else (
        echo config.json not found!
    )
)
if /i "%config_choice%"=="B" goto MAIN_MENU

echo.
echo Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:INFO
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║        📊 PROJECT INFORMATION        ║
echo ╚══════════════════════════════════════╝
echo.
echo CyberTrap RAT v1.0
echo Professional Remote Administration Tool
echo.
echo 🔧 Technology Stack:
echo   - Python 3.7+
echo   - PyQt5 (GUI Framework)
echo   - Cryptography (AES-256 Encryption)
echo   - Socket Programming (Network Communication)
echo.
echo 🎯 Features:
echo   - Professional Dark Theme GUI
echo   - AES-256 Encrypted Communication
echo   - Multi-client Support
echo   - Advanced Keylogger
echo   - Screen Capture & Streaming
echo   - Remote Command Execution
echo   - System Information Collection
echo   - Client Builder with Persistence
echo.
echo 📁 Project Structure:
echo   - server/     : Server application
echo   - client/     : Client application
echo   - builder/    : Client builder
echo   - config.json : Configuration
echo.
echo 🔒 Security Features:
echo   - AES-256 Encryption
echo   - Secure Handshake Protocol
echo   - Anti-detection Mechanisms
echo   - Process Hiding Capabilities
echo.
echo ⚖️ Legal Notice:
echo   This tool is for EDUCATIONAL PURPOSES ONLY!
echo   Only use on systems you own or have permission to test.
echo   Unauthorized access is illegal and unethical.
echo.
echo 📞 Support:
echo   - Check README.md for usage instructions
echo   - Review PROJECT_INFO.md for technical details
echo   - Run test suite to verify functionality
echo.
echo Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:EXIT
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║              👋 GOODBYE              ║
echo ╚══════════════════════════════════════╝
echo.
echo Thank you for using CyberTrap RAT!
echo.
echo ⚠️ REMEMBER:
echo   - Use only for educational purposes
echo   - Respect privacy and laws
echo   - Test only on authorized systems
echo   - Contribute to cybersecurity knowledge
echo.
echo Stay ethical, stay legal! 🛡️
echo.
timeout /t 3
exit /b 0
