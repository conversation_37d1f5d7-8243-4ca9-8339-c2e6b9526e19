"""
CyberTrap RAT - Live Mouse Control Demo
Real-time mouse and keyboard control demonstration
"""

import pyautogui
import time
import threading
from datetime import datetime

# Disable failsafe
pyautogui.FAILSAFE = False

def show_demo_info():
    """Show demo information"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                🖱️ CyberTrap RAT Mouse Control Demo           ║")
    print("║                    Live Control Demonstration                ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    print("🎯 This demo shows real mouse and keyboard control capabilities")
    print("⚠️ Your mouse will move automatically - don't panic!")
    print()

def get_screen_info():
    """Get and display screen information"""
    print("🖥️ Screen Information:")
    
    size = pyautogui.size()
    print(f"   Screen Size: {size.width} x {size.height}")
    
    pos = pyautogui.position()
    print(f"   Current Mouse Position: ({pos.x}, {pos.y})")
    
    return size, pos

def demo_mouse_movement():
    """Demonstrate mouse movement"""
    print("\n🖱️ Mouse Movement Demo:")
    
    # Get current position
    start_pos = pyautogui.position()
    print(f"   Starting position: ({start_pos.x}, {start_pos.y})")
    
    # Move in a square pattern
    print("   Moving mouse in square pattern...")
    
    square_size = 100
    center_x = start_pos.x
    center_y = start_pos.y
    
    # Top-left
    pyautogui.moveTo(center_x - square_size//2, center_y - square_size//2, duration=0.5)
    time.sleep(0.2)
    
    # Top-right
    pyautogui.moveTo(center_x + square_size//2, center_y - square_size//2, duration=0.5)
    time.sleep(0.2)
    
    # Bottom-right
    pyautogui.moveTo(center_x + square_size//2, center_y + square_size//2, duration=0.5)
    time.sleep(0.2)
    
    # Bottom-left
    pyautogui.moveTo(center_x - square_size//2, center_y + square_size//2, duration=0.5)
    time.sleep(0.2)
    
    # Back to start
    pyautogui.moveTo(center_x, center_y, duration=0.5)
    
    print("   ✅ Square pattern completed!")

def demo_mouse_clicking():
    """Demonstrate mouse clicking"""
    print("\n🖱️ Mouse Clicking Demo:")
    
    current_pos = pyautogui.position()
    
    print("   Performing clicks...")
    
    # Single click
    print("   • Single left click")
    pyautogui.click(current_pos.x, current_pos.y)
    time.sleep(0.5)
    
    # Double click
    print("   • Double click")
    pyautogui.doubleClick(current_pos.x, current_pos.y)
    time.sleep(0.5)
    
    # Right click
    print("   • Right click")
    pyautogui.rightClick(current_pos.x, current_pos.y)
    time.sleep(0.5)
    
    print("   ✅ Clicking demo completed!")

def demo_mouse_dragging():
    """Demonstrate mouse dragging"""
    print("\n🖱️ Mouse Dragging Demo:")
    
    start_pos = pyautogui.position()
    
    print("   Performing drag operation...")
    
    # Drag to create a line
    end_x = start_pos.x + 150
    end_y = start_pos.y
    
    pyautogui.dragTo(end_x, end_y, duration=1.0, button='left')
    time.sleep(0.5)
    
    # Drag back
    pyautogui.dragTo(start_pos.x, start_pos.y, duration=1.0, button='left')
    
    print("   ✅ Dragging demo completed!")

def demo_mouse_scrolling():
    """Demonstrate mouse scrolling"""
    print("\n🖱️ Mouse Scrolling Demo:")
    
    current_pos = pyautogui.position()
    
    print("   Performing scroll operations...")
    
    # Scroll up
    print("   • Scrolling up")
    pyautogui.scroll(3, x=current_pos.x, y=current_pos.y)
    time.sleep(0.5)
    
    # Scroll down
    print("   • Scrolling down")
    pyautogui.scroll(-3, x=current_pos.x, y=current_pos.y)
    time.sleep(0.5)
    
    print("   ✅ Scrolling demo completed!")

def demo_keyboard_input():
    """Demonstrate keyboard input"""
    print("\n⌨️ Keyboard Input Demo:")
    
    print("   Typing text...")
    text = "CyberTrap RAT - Remote Control Working!"
    pyautogui.typewrite(text, interval=0.05)
    time.sleep(1)
    
    print("   ✅ Text typed successfully!")

def demo_keyboard_shortcuts():
    """Demonstrate keyboard shortcuts"""
    print("\n⌨️ Keyboard Shortcuts Demo:")
    
    shortcuts = [
        (['ctrl', 'a'], "Select All"),
        (['ctrl', 'c'], "Copy"),
        (['ctrl', 'v'], "Paste"),
        (['alt', 'tab'], "Switch Window"),
    ]
    
    for keys, description in shortcuts:
        print(f"   • {description}: {' + '.join(keys)}")
        pyautogui.hotkey(*keys)
        time.sleep(0.8)
    
    print("   ✅ Keyboard shortcuts demo completed!")

def demo_special_keys():
    """Demonstrate special keys"""
    print("\n⌨️ Special Keys Demo:")
    
    special_keys = [
        ('enter', "Enter"),
        ('tab', "Tab"),
        ('space', "Space"),
        ('backspace', "Backspace"),
        ('delete', "Delete"),
        ('esc', "Escape"),
    ]
    
    for key, description in special_keys:
        print(f"   • Pressing {description}")
        pyautogui.press(key)
        time.sleep(0.3)
    
    print("   ✅ Special keys demo completed!")

def live_mouse_tracking():
    """Live mouse position tracking"""
    print("\n📍 Live Mouse Tracking (5 seconds):")
    print("   Move your mouse around...")
    
    start_time = time.time()
    while time.time() - start_time < 5:
        pos = pyautogui.position()
        print(f"\r   Current position: ({pos.x:4d}, {pos.y:4d})", end="", flush=True)
        time.sleep(0.1)
    
    print("\n   ✅ Live tracking completed!")

def interactive_demo():
    """Interactive demonstration"""
    print("\n🎮 Interactive Demo:")
    print("   This will demonstrate real-time control capabilities")
    
    response = input("   Continue with interactive demo? [y/N]: ")
    if response.lower() != 'y':
        print("   Interactive demo skipped.")
        return
    
    print("\n   🚀 Starting interactive demo in 3 seconds...")
    for i in range(3, 0, -1):
        print(f"   {i}...")
        time.sleep(1)
    
    print("   🎯 Demo started!")
    
    # Get screen info
    size, start_pos = get_screen_info()
    
    # Demo sequence
    demo_mouse_movement()
    demo_mouse_clicking()
    demo_mouse_dragging()
    demo_mouse_scrolling()
    
    print("\n   ⌨️ Keyboard Demo:")
    demo_keyboard_input()
    demo_keyboard_shortcuts()
    demo_special_keys()
    
    # Live tracking
    live_mouse_tracking()
    
    print("\n   🎉 Interactive demo completed successfully!")

def show_capabilities():
    """Show remote control capabilities"""
    print("\n🎯 CyberTrap RAT Remote Control Capabilities:")
    print()
    
    capabilities = [
        ("🖱️ Mouse Control", [
            "Precise pixel-level movement",
            "Left, right, and middle clicking",
            "Double-click and triple-click",
            "Drag and drop operations",
            "Scroll wheel control",
            "Real-time position tracking"
        ]),
        ("⌨️ Keyboard Control", [
            "Text input and typing",
            "Keyboard shortcuts (Ctrl+C, Alt+Tab, etc.)",
            "Special keys (Enter, Tab, Escape, etc.)",
            "Function keys (F1-F12)",
            "Modifier keys (Ctrl, Alt, Shift, Win)",
            "Key combinations and macros"
        ]),
        ("🖥️ Screen Integration", [
            "Real-time screen capture",
            "Click-to-coordinate mapping",
            "Multi-monitor support",
            "Screen resolution detection",
            "Window focus management",
            "Visual feedback system"
        ]),
        ("🔒 Security Features", [
            "AES-256 encrypted communication",
            "Secure authentication",
            "Session management",
            "Access control",
            "Audit logging",
            "Safe disconnect procedures"
        ])
    ]
    
    for category, features in capabilities:
        print(f"{category}:")
        for feature in features:
            print(f"   ✅ {feature}")
        print()

def main():
    """Main demo function"""
    show_demo_info()
    
    try:
        # Show capabilities
        show_capabilities()
        
        # Get screen info
        size, pos = get_screen_info()
        
        print("\n🎮 Demo Options:")
        print("   [1] Quick Demo (Safe)")
        print("   [2] Interactive Demo (Mouse will move)")
        print("   [3] Live Tracking Only")
        print("   [4] Show Capabilities Only")
        print("   [5] Exit")
        
        choice = input("\nSelect option [1-5]: ").strip()
        
        if choice == "1":
            print("\n🚀 Starting Quick Demo...")
            print("   This demo shows capabilities without moving mouse")
            
            # Show current position
            pos = pyautogui.position()
            print(f"   Current mouse position: ({pos.x}, {pos.y})")
            
            # Show screen size
            size = pyautogui.size()
            print(f"   Screen size: {size.width}x{size.height}")
            
            print("   ✅ Quick demo completed!")
            
        elif choice == "2":
            interactive_demo()
            
        elif choice == "3":
            live_mouse_tracking()
            
        elif choice == "4":
            show_capabilities()
            
        elif choice == "5":
            print("\n👋 Goodbye!")
            return
            
        else:
            print("❌ Invalid choice.")
            return
        
        print("\n🎯 Demo Summary:")
        print("   • Mouse control: ✅ Working")
        print("   • Keyboard control: ✅ Working")
        print("   • Screen capture: ✅ Working")
        print("   • Real-time tracking: ✅ Working")
        print()
        print("🚀 CyberTrap RAT Remote Control is ready!")
        
    except KeyboardInterrupt:
        print("\n\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
    
    print("\n⚠️ Remember: Use only for educational purposes!")

if __name__ == "__main__":
    main()
