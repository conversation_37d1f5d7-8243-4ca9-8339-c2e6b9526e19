"""
Quick test to verify CyberTrap RAT functionality
"""

import sys
import os
import time
import threading

def test_server():
    """Test server functionality"""
    print("🚀 Testing Server...")
    try:
        sys.path.append('server/core')
        from server import CyberTrapServer
        
        # Create server
        server = CyberTrapServer(host="127.0.0.1", port=4445, encryption_key="test_key")
        
        # Test server creation
        print("   ✅ Server created successfully")
        
        # Start server in thread
        def run_server():
            try:
                server.start_server()
            except Exception as e:
                print(f"   Server error: {e}")
        
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()
        
        time.sleep(2)
        print("   ✅ Server started in background")
        
        # Stop server
        server.stop_server()
        print("   ✅ Server stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Server test failed: {e}")
        return False

def test_client():
    """Test client functionality"""
    print("\n🖥️ Testing Client...")
    try:
        sys.path.append('client')
        sys.path.append('client/modules')
        sys.path.append('client/utils')
        
        from client import CyberTrapClient
        
        # Create client
        client = CyberTrapClient("127.0.0.1", 4446, "test_key")
        print("   ✅ Client created successfully")
        
        # Test system info
        sys_info = client.system_info.get_info()
        print(f"   ✅ System info collected: {sys_info.get('computer_name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Client test failed: {e}")
        return False

def test_encryption():
    """Test encryption"""
    print("\n🔒 Testing Encryption...")
    try:
        sys.path.append('server/core')
        from encryption import CyberTrapEncryption
        
        enc = CyberTrapEncryption("test_password")
        
        # Test data
        test_data = "Hello CyberTrap!"
        encrypted = enc.encrypt(test_data)
        decrypted = enc.decrypt(encrypted)
        
        if test_data == decrypted:
            print("   ✅ Encryption/Decryption working")
            return True
        else:
            print("   ❌ Encryption/Decryption failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Encryption test failed: {e}")
        return False

def main():
    """Main test function"""
    print("╔══════════════════════════════════════╗")
    print("║        🔥 CyberTrap RAT Quick Test   ║")
    print("╚══════════════════════════════════════╝")
    print()
    
    results = []
    
    # Run tests
    results.append(test_encryption())
    results.append(test_server())
    results.append(test_client())
    
    # Summary
    print("\n" + "="*50)
    passed = sum(results)
    total = len(results)
    
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! CyberTrap RAT is ready to use.")
        print("\nNext steps:")
        print("1. Run the server: python server/main.py")
        print("2. Run the client: python client/client.py")
        print("3. Test the GUI interface")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    print("\n⚠️ Remember: Use only for educational purposes!")

if __name__ == "__main__":
    main()
