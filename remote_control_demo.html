<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖱️ CyberTrap RAT - Remote Control Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e2328, #2f3542);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(30, 35, 40, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #3742fa;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            color: #ff6b6b;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .demo-section {
            background: #2f3542;
            border-radius: 10px;
            padding: 25px;
            border-left: 4px solid #3742fa;
        }
        
        .demo-section h3 {
            color: #3742fa;
            margin-top: 0;
            font-size: 1.4em;
        }
        
        .control-demo {
            background: #1e2328;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #57606f;
            position: relative;
            overflow: hidden;
        }
        
        .mouse-area {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #2f3542, #1e2328);
            border: 2px dashed #3742fa;
            border-radius: 8px;
            position: relative;
            cursor: crosshair;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1em;
            color: #3742fa;
        }
        
        .mouse-cursor {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff6b6b;
            border-radius: 50%;
            pointer-events: none;
            transition: all 0.1s ease;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }
        
        .keyboard-demo {
            background: #2f3542;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .key {
            display: inline-block;
            background: #3742fa;
            color: white;
            padding: 8px 12px;
            margin: 3px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .key:hover {
            background: #2f32e2;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(55, 66, 250, 0.3);
        }
        
        .key.pressed {
            background: #ff6b6b;
            transform: scale(0.95);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: #2f3542;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid #57606f;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #3742fa;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(55, 66, 250, 0.2);
        }
        
        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        
        .demo-button {
            background: #3742fa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            background: #2f32e2;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(55, 66, 250, 0.3);
        }
        
        .demo-button.success {
            background: #2ed573;
        }
        
        .demo-button.success:hover {
            background: #26c464;
        }
        
        .demo-button.danger {
            background: #ff4757;
        }
        
        .demo-button.danger:hover {
            background: #e63946;
        }
        
        .status-display {
            background: #1e2328;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #2ed573;
        }
        
        .coordinates {
            color: #ffa502;
            font-weight: bold;
        }
        
        .action-log {
            background: #1e2328;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            padding: 3px 0;
            border-bottom: 1px solid #57606f;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .warning-box {
            background: #ff4757;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .pulse {
            animation: pulse 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖱️ CyberTrap RAT Remote Control</h1>
            <p>Professional Mouse & Keyboard Control Demo</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-section">
                <h3>🖱️ Mouse Control Demo</h3>
                <div class="mouse-area" id="mouseArea">
                    <div class="mouse-cursor" id="mouseCursor"></div>
                    Move your mouse here to simulate remote control
                </div>
                
                <div class="status-display">
                    <div>Mouse Position: <span class="coordinates" id="mouseCoords">(0, 0)</span></div>
                    <div>Last Action: <span id="lastAction">None</span></div>
                    <div>Click Count: <span id="clickCount">0</span></div>
                </div>
                
                <div style="text-align: center; margin-top: 15px;">
                    <button class="demo-button" onclick="simulateClick('left')">Left Click</button>
                    <button class="demo-button" onclick="simulateClick('right')">Right Click</button>
                    <button class="demo-button" onclick="simulateDoubleClick()">Double Click</button>
                    <button class="demo-button" onclick="simulateScroll('up')">Scroll Up</button>
                    <button class="demo-button" onclick="simulateScroll('down')">Scroll Down</button>
                </div>
            </div>
            
            <div class="demo-section">
                <h3>⌨️ Keyboard Control Demo</h3>
                <div class="keyboard-demo">
                    <div style="margin-bottom: 15px;">
                        <strong>Common Shortcuts:</strong>
                    </div>
                    <div class="key" onclick="simulateKey('Ctrl+C')">Ctrl+C</div>
                    <div class="key" onclick="simulateKey('Ctrl+V')">Ctrl+V</div>
                    <div class="key" onclick="simulateKey('Ctrl+A')">Ctrl+A</div>
                    <div class="key" onclick="simulateKey('Alt+Tab')">Alt+Tab</div>
                    <div class="key" onclick="simulateKey('Win+R')">Win+R</div>
                    <div class="key" onclick="simulateKey('Ctrl+Z')">Ctrl+Z</div>
                </div>
                
                <div class="keyboard-demo">
                    <div style="margin-bottom: 15px;">
                        <strong>Function Keys:</strong>
                    </div>
                    <div class="key" onclick="simulateKey('F1')">F1</div>
                    <div class="key" onclick="simulateKey('F2')">F2</div>
                    <div class="key" onclick="simulateKey('F5')">F5</div>
                    <div class="key" onclick="simulateKey('F11')">F11</div>
                    <div class="key" onclick="simulateKey('F12')">F12</div>
                </div>
                
                <div class="status-display">
                    <div>Last Key: <span class="coordinates" id="lastKey">None</span></div>
                    <div>Key Count: <span id="keyCount">0</span></div>
                </div>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h4>Precise Control</h4>
                <p>Pixel-perfect mouse movement and clicking with real-time feedback</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h4>Real-time Response</h4>
                <p>Instant mouse and keyboard actions with minimal latency</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🖥️</div>
                <h4>Screen Capture</h4>
                <p>Live screen updates for visual feedback during control</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🪟</div>
                <h4>Window Management</h4>
                <p>Switch between windows and manage applications remotely</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h4>Secure Connection</h4>
                <p>AES-256 encrypted communication for safe remote control</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎮</div>
                <h4>Professional GUI</h4>
                <p>User-friendly interface with advanced control options</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📊 Action Log</h3>
            <div class="action-log" id="actionLog">
                <div class="log-entry">[INFO] Remote control demo initialized</div>
                <div class="log-entry">[INFO] Mouse and keyboard simulation ready</div>
                <div class="log-entry">[INFO] Waiting for user interaction...</div>
            </div>
            
            <div style="text-align: center; margin-top: 15px;">
                <button class="demo-button success" onclick="clearLog()">Clear Log</button>
                <button class="demo-button" onclick="exportLog()">Export Log</button>
                <button class="demo-button danger" onclick="resetDemo()">Reset Demo</button>
            </div>
        </div>
        
        <div class="warning-box">
            ⚠️ FOR EDUCATIONAL PURPOSES ONLY - Use responsibly and ethically!
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="demo-button success" onclick="window.open('file:///c:/Users/<USER>/Desktop/for3on/start_remote_control.py', '_blank')">🚀 Launch Remote Control</button>
            <button class="demo-button" onclick="window.open('file:///c:/Users/<USER>/Desktop/for3on', '_blank')">📁 Open Project</button>
        </div>
    </div>
    
    <script>
        let mouseX = 0, mouseY = 0;
        let clickCount = 0;
        let keyCount = 0;
        
        // Mouse tracking
        document.getElementById('mouseArea').addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            mouseX = e.clientX - rect.left;
            mouseY = e.clientY - rect.top;
            
            document.getElementById('mouseCoords').textContent = `(${Math.round(mouseX)}, ${Math.round(mouseY)})`;
            
            const cursor = document.getElementById('mouseCursor');
            cursor.style.left = mouseX - 10 + 'px';
            cursor.style.top = mouseY - 10 + 'px';
        });
        
        // Mouse clicking
        document.getElementById('mouseArea').addEventListener('click', function(e) {
            simulateClick('left');
        });
        
        document.getElementById('mouseArea').addEventListener('contextmenu', function(e) {
            e.preventDefault();
            simulateClick('right');
        });
        
        function simulateClick(button) {
            clickCount++;
            document.getElementById('clickCount').textContent = clickCount;
            document.getElementById('lastAction').textContent = `${button} click at (${Math.round(mouseX)}, ${Math.round(mouseY)})`;
            
            addLogEntry(`[MOUSE] ${button} click at coordinates (${Math.round(mouseX)}, ${Math.round(mouseY)})`);
            
            // Visual feedback
            const cursor = document.getElementById('mouseCursor');
            cursor.classList.add('pulse');
            setTimeout(() => cursor.classList.remove('pulse'), 500);
        }
        
        function simulateDoubleClick() {
            clickCount += 2;
            document.getElementById('clickCount').textContent = clickCount;
            document.getElementById('lastAction').textContent = `Double click at (${Math.round(mouseX)}, ${Math.round(mouseY)})`;
            
            addLogEntry(`[MOUSE] Double click at coordinates (${Math.round(mouseX)}, ${Math.round(mouseY)})`);
        }
        
        function simulateScroll(direction) {
            document.getElementById('lastAction').textContent = `Scroll ${direction} at (${Math.round(mouseX)}, ${Math.round(mouseY)})`;
            addLogEntry(`[MOUSE] Scroll ${direction} at coordinates (${Math.round(mouseX)}, ${Math.round(mouseY)})`);
        }
        
        function simulateKey(key) {
            keyCount++;
            document.getElementById('keyCount').textContent = keyCount;
            document.getElementById('lastKey').textContent = key;
            
            addLogEntry(`[KEYBOARD] Key combination: ${key}`);
            
            // Visual feedback
            event.target.classList.add('pressed');
            setTimeout(() => event.target.classList.remove('pressed'), 200);
        }
        
        function addLogEntry(message) {
            const log = document.getElementById('actionLog');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${timestamp}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            // Keep only last 20 entries
            while (log.children.length > 20) {
                log.removeChild(log.firstChild);
            }
        }
        
        function clearLog() {
            document.getElementById('actionLog').innerHTML = '<div class="log-entry">[INFO] Log cleared</div>';
        }
        
        function exportLog() {
            const log = document.getElementById('actionLog');
            const entries = Array.from(log.children).map(entry => entry.textContent).join('\n');
            
            const blob = new Blob([entries], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'cybertrap_remote_control_log.txt';
            a.click();
            URL.revokeObjectURL(url);
            
            addLogEntry('[SYSTEM] Log exported successfully');
        }
        
        function resetDemo() {
            clickCount = 0;
            keyCount = 0;
            mouseX = 0;
            mouseY = 0;
            
            document.getElementById('clickCount').textContent = '0';
            document.getElementById('keyCount').textContent = '0';
            document.getElementById('mouseCoords').textContent = '(0, 0)';
            document.getElementById('lastAction').textContent = 'None';
            document.getElementById('lastKey').textContent = 'None';
            
            clearLog();
            addLogEntry('[SYSTEM] Demo reset successfully');
        }
        
        // Auto-update demo
        setInterval(() => {
            const time = new Date().toLocaleTimeString();
            // Add some dynamic content
        }, 1000);
        
        // Initialize
        addLogEntry('[SYSTEM] CyberTrap Remote Control Demo loaded successfully');
    </script>
</body>
</html>
