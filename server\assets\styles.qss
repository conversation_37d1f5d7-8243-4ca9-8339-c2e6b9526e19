/* CyberTrap RAT - Dark Theme Stylesheet */

/* Main Window */
QMainWindow {
    background-color: #1e2328;
    color: #ffffff;
    font-family: "Segoe UI", Arial, sans-serif;
}

/* Widgets */
QWidget {
    background-color: #1e2328;
    color: #ffffff;
    border: none;
}

/* Buttons */
QPushButton {
    background-color: #3742fa;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: bold;
    font-size: 12px;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #2f32e2;
}

QPushButton:pressed {
    background-color: #1e22c7;
}

QPushButton:disabled {
    background-color: #57606f;
    color: #a4b0be;
}

/* Success Button */
QPushButton[class="success"] {
    background-color: #2ed573;
}

QPushButton[class="success"]:hover {
    background-color: #26c464;
}

/* Danger Button */
QPushButton[class="danger"] {
    background-color: #ff4757;
}

QPushButton[class="danger"]:hover {
    background-color: #e63946;
}

/* Warning Button */
QPushButton[class="warning"] {
    background-color: #ffa502;
}

QPushButton[class="warning"]:hover {
    background-color: #e6940a;
}

/* Tables */
QTableWidget {
    background-color: #2f3542;
    border: 1px solid #57606f;
    gridline-color: #57606f;
    color: white;
    selection-background-color: #3742fa;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #57606f;
}

QTableWidget::item:selected {
    background-color: #3742fa;
    color: white;
}

QHeaderView::section {
    background-color: #1e2328;
    color: white;
    padding: 8px;
    border: 1px solid #57606f;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #2f3542;
}

/* Text Edits */
QTextEdit, QPlainTextEdit {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    padding: 5px;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border: 2px solid #3742fa;
}

/* Line Edits */
QLineEdit {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
    padding: 8px;
    border-radius: 3px;
}

QLineEdit:focus {
    border: 2px solid #3742fa;
}

/* Spin Boxes */
QSpinBox {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
    padding: 5px;
    border-radius: 3px;
}

QSpinBox:focus {
    border: 2px solid #3742fa;
}

/* Combo Boxes */
QComboBox {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
    padding: 5px;
    border-radius: 3px;
}

QComboBox:focus {
    border: 2px solid #3742fa;
}

QComboBox::drop-down {
    border: none;
    background-color: #57606f;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
    selection-background-color: #3742fa;
}

/* Labels */
QLabel {
    color: white;
    background-color: transparent;
}

QLabel[class="header"] {
    font-size: 18px;
    font-weight: bold;
    color: #3742fa;
}

QLabel[class="success"] {
    color: #2ed573;
    font-weight: bold;
}

QLabel[class="danger"] {
    color: #ff4757;
    font-weight: bold;
}

QLabel[class="warning"] {
    color: #ffa502;
    font-weight: bold;
}

/* Group Boxes */
QGroupBox {
    color: white;
    border: 2px solid #57606f;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #3742fa;
}

/* Splitters */
QSplitter::handle {
    background-color: #57606f;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* Menu Bar */
QMenuBar {
    background-color: #1e2328;
    color: white;
    border-bottom: 1px solid #57606f;
}

QMenuBar::item {
    background-color: transparent;
    padding: 5px 10px;
}

QMenuBar::item:selected {
    background-color: #3742fa;
    border-radius: 3px;
}

QMenuBar::item:pressed {
    background-color: #2f32e2;
}

/* Menus */
QMenu {
    background-color: #2f3542;
    color: white;
    border: 1px solid #57606f;
    border-radius: 3px;
}

QMenu::item {
    padding: 8px 20px;
    background-color: transparent;
}

QMenu::item:selected {
    background-color: #3742fa;
}

QMenu::separator {
    height: 1px;
    background-color: #57606f;
    margin: 5px 0;
}

/* Status Bar */
QStatusBar {
    background-color: #1e2328;
    color: white;
    border-top: 1px solid #57606f;
}

QStatusBar::item {
    border: none;
}

/* Tool Tips */
QToolTip {
    background-color: #2f3542;
    color: white;
    border: 1px solid #57606f;
    padding: 5px;
    border-radius: 3px;
}

/* Progress Bars */
QProgressBar {
    background-color: #2f3542;
    border: 1px solid #57606f;
    border-radius: 3px;
    text-align: center;
    color: white;
}

QProgressBar::chunk {
    background-color: #3742fa;
    border-radius: 2px;
}

/* Check Boxes */
QCheckBox {
    color: white;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    background-color: #2f3542;
    border: 1px solid #57606f;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    background-color: #3742fa;
    border: 1px solid #3742fa;
    border-radius: 3px;
}

/* Radio Buttons */
QRadioButton {
    color: white;
    spacing: 5px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 9px;
}

QRadioButton::indicator:unchecked {
    background-color: #2f3542;
    border: 1px solid #57606f;
}

QRadioButton::indicator:checked {
    background-color: #3742fa;
    border: 1px solid #3742fa;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #2f3542;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #57606f;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #3742fa;
}

QScrollBar:horizontal {
    background-color: #2f3542;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #57606f;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #3742fa;
}

QScrollBar::add-line, QScrollBar::sub-line {
    border: none;
    background: none;
}

/* Tabs */
QTabWidget::pane {
    border: 1px solid #57606f;
    background-color: #2f3542;
}

QTabBar::tab {
    background-color: #1e2328;
    color: white;
    padding: 8px 16px;
    border: 1px solid #57606f;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #3742fa;
}

QTabBar::tab:hover {
    background-color: #2f3542;
}

/* Dialog Boxes */
QDialog {
    background-color: #1e2328;
    color: white;
}

QDialogButtonBox {
    background-color: transparent;
}

/* List Widgets */
QListWidget {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
}

QListWidget::item {
    padding: 5px;
    border-bottom: 1px solid #57606f;
}

QListWidget::item:selected {
    background-color: #3742fa;
}

QListWidget::item:hover {
    background-color: #57606f;
}

/* Tree Widgets */
QTreeWidget {
    background-color: #2f3542;
    border: 1px solid #57606f;
    color: white;
}

QTreeWidget::item {
    padding: 3px;
}

QTreeWidget::item:selected {
    background-color: #3742fa;
}

QTreeWidget::item:hover {
    background-color: #57606f;
}

/* Sliders */
QSlider::groove:horizontal {
    background-color: #2f3542;
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #3742fa;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #2f32e2;
}
