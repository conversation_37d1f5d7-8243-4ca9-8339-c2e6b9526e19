"""
CyberTrap RAT - Encryption Module
Advanced AES-256 encryption for secure communication
"""

import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os
import json

class CyberTrapEncryption:
    def __init__(self, password: str):
        """Initialize encryption with password"""
        self.password = password.encode()
        self.salt = b'cybertrap_salt_2024'  # في الإنتاج، استخدم salt عشوائي
        self.key = self._derive_key()
        self.cipher = Fernet(self.key)
    
    def _derive_key(self) -> bytes:
        """Derive encryption key from password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return key
    
    def encrypt(self, data: str) -> str:
        """Encrypt string data"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            encrypted = self.cipher.encrypt(data)
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"Encryption error: {e}")
            return ""
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt string data"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode('utf-8')
        except Exception as e:
            print(f"Decryption error: {e}")
            return ""
    
    def encrypt_json(self, data: dict) -> str:
        """Encrypt JSON data"""
        try:
            json_str = json.dumps(data)
            return self.encrypt(json_str)
        except Exception as e:
            print(f"JSON encryption error: {e}")
            return ""
    
    def decrypt_json(self, encrypted_data: str) -> dict:
        """Decrypt JSON data"""
        try:
            decrypted_str = self.decrypt(encrypted_data)
            return json.loads(decrypted_str)
        except Exception as e:
            print(f"JSON decryption error: {e}")
            return {}

# Test function
if __name__ == "__main__":
    # Test encryption
    enc = CyberTrapEncryption("test_password")
    
    test_data = "Hello CyberTrap RAT!"
    encrypted = enc.encrypt(test_data)
    decrypted = enc.decrypt(encrypted)
    
    print(f"Original: {test_data}")
    print(f"Encrypted: {encrypted}")
    print(f"Decrypted: {decrypted}")
    print(f"Success: {test_data == decrypted}")
