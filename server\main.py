"""
CyberTrap RAT - Main Server Application
Professional Remote Administration Tool
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont
from gui.main_window import CyberTrapMainWindow

def setup_application():
    """Setup application properties"""
    app = QApplication(sys.argv)
    app.setApplicationName("CyberTrap RAT")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("CyberTrap")
    
    # Set application icon
    if os.path.exists('server/assets/icons/cybertrap.ico'):
        app.setWindowIcon(QIcon('server/assets/icons/cybertrap.ico'))
    
    # Set default font
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    # Enable high DPI scaling
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app

def check_requirements():
    """Check if all requirements are met"""
    try:
        # Check config file
        if not os.path.exists('config.json'):
            QMessageBox.critical(None, "Error", "config.json file not found!")
            return False
        
        # Check config validity
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        required_keys = ['server', 'client', 'gui']
        for key in required_keys:
            if key not in config:
                QMessageBox.critical(None, "Error", f"Invalid config: missing '{key}' section!")
                return False
        
        # Create logs directory
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # Create assets directory
        if not os.path.exists('server/assets/icons'):
            os.makedirs('server/assets/icons')
        
        return True
        
    except Exception as e:
        QMessageBox.critical(None, "Error", f"Requirements check failed: {str(e)}")
        return False

def show_splash():
    """Show splash screen with logo"""
    splash_text = """
    ╔══════════════════════════════════════╗
    ║           🔥 CyberTrap RAT           ║
    ║        Professional RAT v1.0         ║
    ║                                      ║
    ║  ⚠️  FOR EDUCATIONAL PURPOSES ONLY   ║
    ║                                      ║
    ║  Features:                           ║
    ║  • 🔒 AES-256 Encryption            ║
    ║  • 📷 Screen Capture & Streaming     ║
    ║  • ⌨️ Advanced Keylogger             ║
    ║  • 📁 File Management                ║
    ║  • 🎥 Camera & Microphone            ║
    ║  • 💻 Remote Shell Access            ║
    ║  • 🔑 Password Extraction            ║
    ║  • 🖱️ Remote Control                 ║
    ║                                      ║
    ║         Starting Application...      ║
    ╚══════════════════════════════════════╝
    """
    print(splash_text)

def main():
    """Main application entry point"""
    try:
        # Show splash
        show_splash()
        
        # Setup application
        app = setup_application()
        
        # Check requirements
        if not check_requirements():
            sys.exit(1)
        
        # Create and show main window
        window = CyberTrapMainWindow()
        window.show()
        
        # Start application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"Fatal error: {e}")
        if 'app' in locals():
            QMessageBox.critical(None, "Fatal Error", f"Application failed to start: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
