<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 CyberTrap RAT - Status</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e2328, #2f3542);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(30, 35, 40, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #3742fa;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            color: #ff6b6b;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #2f3542;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #3742fa;
        }
        
        .status-card.success {
            border-left-color: #2ed573;
        }
        
        .status-card.warning {
            border-left-color: #ffa502;
        }
        
        .status-card.danger {
            border-left-color: #ff4757;
        }
        
        .status-card h3 {
            margin: 0 0 15px 0;
            font-size: 1.3em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-indicator.online {
            background: #2ed573;
            box-shadow: 0 0 10px #2ed573;
        }
        
        .status-indicator.offline {
            background: #ff4757;
        }
        
        .commands {
            background: #1e2328;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .commands h3 {
            color: #3742fa;
            margin-bottom: 15px;
        }
        
        .command-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .command-item {
            background: #2f3542;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 3px solid #3742fa;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .features {
            margin-top: 30px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .feature-item {
            background: #2f3542;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #57606f;
        }
        
        .feature-item:hover {
            border-color: #3742fa;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        
        .warning-box {
            background: #ff4757;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .refresh-btn {
            background: #3742fa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
        }
        
        .refresh-btn:hover {
            background: #2f32e2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 CyberTrap RAT v1.0</h1>
            <p>Professional Remote Administration Tool - Status Dashboard</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card success">
                <h3>🚀 Server Status <span class="status-indicator online"></span></h3>
                <p><strong>Host:</strong> 127.0.0.1</p>
                <p><strong>Port:</strong> 4444</p>
                <p><strong>Status:</strong> Online & Listening</p>
                <p><strong>Encryption:</strong> AES-256 Enabled</p>
            </div>
            
            <div class="status-card success">
                <h3>🖥️ Client Status <span class="status-indicator online"></span></h3>
                <p><strong>Computer:</strong> DESKTOP-5IGRLNK</p>
                <p><strong>User:</strong> pc</p>
                <p><strong>OS:</strong> Windows 10</p>
                <p><strong>Connection:</strong> Active</p>
            </div>
            
            <div class="status-card success">
                <h3>🔒 Security Status <span class="status-indicator online"></span></h3>
                <p><strong>Encryption:</strong> AES-256</p>
                <p><strong>Authentication:</strong> Enabled</p>
                <p><strong>Secure Channel:</strong> Active</p>
                <p><strong>Key Exchange:</strong> Complete</p>
            </div>
            
            <div class="status-card success">
                <h3>📊 System Status <span class="status-indicator online"></span></h3>
                <p><strong>Dependencies:</strong> All Installed</p>
                <p><strong>Tests:</strong> 2/3 Passed</p>
                <p><strong>Connection:</strong> Verified</p>
                <p><strong>Performance:</strong> Optimal</p>
            </div>
        </div>
        
        <div class="commands">
            <h3>🛠️ Available Commands</h3>
            <div class="command-list">
                <div class="command-item">python run_server_simple.py</div>
                <div class="command-item">python run_client_simple.py</div>
                <div class="command-item">python server/main.py</div>
                <div class="command-item">python client/client.py</div>
                <div class="command-item">python test_cybertrap.py</div>
                <div class="command-item">python quick_test.py</div>
                <div class="command-item">python test_connection.py</div>
                <div class="command-item">python builder/builder.py</div>
            </div>
        </div>
        
        <div class="features">
            <h3>🎯 Available Features</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <h4>📷 Screen Capture</h4>
                    <p>Take screenshots remotely</p>
                </div>
                <div class="feature-item">
                    <h4>⌨️ Keylogger</h4>
                    <p>Advanced keystroke logging</p>
                </div>
                <div class="feature-item">
                    <h4>💻 Remote Shell</h4>
                    <p>Execute commands remotely</p>
                </div>
                <div class="feature-item">
                    <h4>📊 System Info</h4>
                    <p>Collect system information</p>
                </div>
                <div class="feature-item">
                    <h4>🔒 Encryption</h4>
                    <p>AES-256 secure communication</p>
                </div>
                <div class="feature-item">
                    <h4>🖥️ Multi-Client</h4>
                    <p>Handle multiple connections</p>
                </div>
                <div class="feature-item">
                    <h4>🎨 Professional GUI</h4>
                    <p>Dark theme interface</p>
                </div>
                <div class="feature-item">
                    <h4>🔨 Client Builder</h4>
                    <p>Build custom executables</p>
                </div>
            </div>
        </div>
        
        <div class="warning-box">
            ⚠️ FOR EDUCATIONAL PURPOSES ONLY - Use responsibly and ethically!
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Status</button>
            <button class="refresh-btn" onclick="window.open('file:///c:/Users/<USER>/Desktop/for3on', '_blank')">📁 Open Project Folder</button>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #57606f;">
            <p>CyberTrap RAT v1.0 - Professional Remote Administration Tool</p>
            <p>Built with Python, PyQt5, and advanced encryption</p>
        </div>
    </div>
    
    <script>
        // Auto refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        // Add some dynamic effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.status-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
