"""
Test connection between server and client
"""

import socket
import time
import threading

def test_server_port():
    """Test if server port is open"""
    print("🔍 Testing server port...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 4444))
        sock.close()
        
        if result == 0:
            print("✅ Server port 4444 is open and listening")
            return True
        else:
            print("❌ Server port 4444 is not accessible")
            return False
            
    except Exception as e:
        print(f"❌ Port test error: {e}")
        return False

def test_simple_connection():
    """Test simple connection to server"""
    print("\n🔗 Testing simple connection...")
    
    try:
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.settimeout(10)
        
        print("   Connecting to 127.0.0.1:4444...")
        client_socket.connect(('127.0.0.1', 4444))
        print("   ✅ Connected successfully!")
        
        # Send test message
        test_msg = "TEST_CONNECTION"
        client_socket.send(test_msg.encode('utf-8'))
        print("   📤 Test message sent")
        
        # Receive response
        response = client_socket.recv(1024).decode('utf-8')
        print(f"   📥 Server response: {response[:50]}...")
        
        client_socket.close()
        print("   ✅ Connection test successful!")
        return True
        
    except Exception as e:
        print(f"   ❌ Connection test failed: {e}")
        return False

def main():
    """Main test function"""
    print("╔══════════════════════════════════════╗")
    print("║      🔥 CyberTrap Connection Test    ║")
    print("╚══════════════════════════════════════╝")
    print()
    
    # Test port
    port_ok = test_server_port()
    
    if port_ok:
        # Test connection
        conn_ok = test_simple_connection()
        
        if conn_ok:
            print("\n🎯 All tests passed! Server and client can communicate.")
        else:
            print("\n❌ Connection test failed.")
    else:
        print("\n❌ Server is not running or port is blocked.")
        print("💡 Try running: python run_server_simple.py")
    
    print("\n⚠️ Remember: Use only for educational purposes!")

if __name__ == "__main__":
    main()
